<?xml version="1.0" encoding="UTF-8"?>
<incidents format="6" by="lint 8.9.2" type="incidents">

    <incident
        id="RedundantLabel"
        severity="warning"
        message="Redundant label can be removed">
        <fix-attribute
            description="Delete label"
            namespace="http://schemas.android.com/apk/res/android"
            attribute="label"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*manifest*0}"
            line="24"
            column="13"
            startOffset="1033"
            endLine="24"
            endColumn="45"
            endOffset="1065"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.core:core-ktx than 1.10.1 is available: 1.13.1">
        <fix-replace
            description="Change to 1.13.1"
            family="Update versions"
            oldString="1.10.1"
            replacement="1.13.1"
            priority="0"/>
        <location
            file="../gradle/libs.versions.toml"
            line="4"
            column="11"
            startOffset="53"
            endLine="4"
            endColumn="19"
            endOffset="61"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.lifecycle:lifecycle-runtime-ktx than 2.6.1 is available: 2.8.3">
        <fix-replace
            description="Change to 2.8.3"
            family="Update versions"
            oldString="2.6.1"
            replacement="2.8.3"
            priority="0"/>
        <location
            file="../gradle/libs.versions.toml"
            line="8"
            column="23"
            startOffset="147"
            endLine="8"
            endColumn="30"
            endOffset="154"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.activity:activity-compose than 1.8.0 is available: 1.8.2">
        <fix-replace
            description="Change to 1.8.2"
            family="Update versions"
            oldString="1.8.0"
            replacement="1.8.2"
            priority="0"/>
        <location
            file="../gradle/libs.versions.toml"
            line="9"
            column="19"
            startOffset="173"
            endLine="9"
            endColumn="26"
            endOffset="180"/>
    </incident>

    <incident
        id="AutoboxingStateCreation"
        severity="informational"
        message="Prefer `mutableIntStateOf` instead of `mutableStateOf`">
        <fix-replace
            description="Replace with mutableIntStateOf"
            replacement="androidx.compose.runtime.mutableIntStateOf"
            shortenNames="true"
            priority="0">
            <range
                file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/wendy/face/MainActivity.kt"
                startOffset="3721"
                endOffset="3735"/>
        </fix-replace>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/wendy/face/MainActivity.kt"
            line="84"
            column="46"
            startOffset="3721"
            endLine="84"
            endColumn="60"
            endOffset="3735"/>
    </incident>

    <incident
        id="AutoboxingStateCreation"
        severity="informational"
        message="Prefer `mutableIntStateOf` instead of `mutableStateOf`">
        <fix-replace
            description="Replace with mutableIntStateOf"
            replacement="androidx.compose.runtime.mutableIntStateOf"
            shortenNames="true"
            priority="0">
            <range
                file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/wendy/face/MainActivity.kt"
                startOffset="3787"
                endOffset="3801"/>
        </fix-replace>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/wendy/face/MainActivity.kt"
            line="85"
            column="47"
            startOffset="3787"
            endLine="85"
            endColumn="61"
            endOffset="3801"/>
    </incident>

    <incident
        id="UseTomlInstead"
        severity="warning"
        message="Use version catalog instead">
        <fix-composite
            description="Replace with new library catalog declaration for mlkit-face-detection"
            robot="true">
            <fix-replace
                description="Replace with faceDetectionVersion = &quot;16.1.6&quot;..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="faceDetectionVersion = &quot;16.1.6&quot;&#xA;"
                priority="0">
                <range
                    file="../gradle/libs.versions.toml"
                    startOffset="25"
                    endOffset="25"/>
            </fix-replace>
            <fix-replace
                description="Replace with mlkit-face-detection = { module = &quot;com.google.mlkit:face-detection&quot;, version.ref = &quot;faceDetectionVersion&quot; }..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="mlkit-face-detection = { module = &quot;com.google.mlkit:face-detection&quot;, version.ref = &quot;faceDetectionVersion&quot; }&#xA;"
                priority="0">
                <range
                    file="../gradle/libs.versions.toml"
                    startOffset="1515"
                    endOffset="1515"/>
            </fix-replace>
            <fix-replace
                description="Replace with libs.mlkit.face.detection"
                robot="true"
                replacement="libs.mlkit.face.detection"
                priority="0">
                <range
                    file="${:app*projectDir}/build.gradle.kts"
                    startOffset="963"
                    endOffset="1003"/>
            </fix-replace>
        </fix-composite>
        <location
            file="${:app*projectDir}/build.gradle.kts"
            line="44"
            column="20"
            startOffset="963"
            endLine="44"
            endColumn="60"
            endOffset="1003"/>
    </incident>

    <incident
        id="UseTomlInstead"
        severity="warning"
        message="Use version catalog instead">
        <fix-composite
            description="Replace with new library catalog declaration for camera-core"
            robot="true">
            <fix-replace
                description="Replace with cameraCoreVersion = &quot;1.3.1&quot;..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="cameraCoreVersion = &quot;1.3.1&quot;&#xA;"
                priority="0">
                <range
                    file="../gradle/libs.versions.toml"
                    startOffset="25"
                    endOffset="25"/>
            </fix-replace>
            <fix-replace
                description="Replace with camera-core = { module = &quot;androidx.camera:camera-core&quot;, version.ref = &quot;cameraCoreVersion&quot; }..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="camera-core = { module = &quot;androidx.camera:camera-core&quot;, version.ref = &quot;cameraCoreVersion&quot; }&#xA;"
                priority="0">
                <range
                    file="../gradle/libs.versions.toml"
                    startOffset="312"
                    endOffset="312"/>
            </fix-replace>
            <fix-replace
                description="Replace with libs.camera.core"
                robot="true"
                replacement="libs.camera.core"
                priority="0">
                <range
                    file="${:app*projectDir}/build.gradle.kts"
                    startOffset="1073"
                    endOffset="1108"/>
            </fix-replace>
        </fix-composite>
        <location
            file="${:app*projectDir}/build.gradle.kts"
            line="48"
            column="20"
            startOffset="1073"
            endLine="48"
            endColumn="55"
            endOffset="1108"/>
    </incident>

    <incident
        id="UseTomlInstead"
        severity="warning"
        message="Use version catalog instead">
        <fix-composite
            description="Replace with new library catalog declaration for camera-camera2"
            robot="true">
            <fix-replace
                description="Replace with cameraCamera2Version = &quot;1.3.1&quot;..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="cameraCamera2Version = &quot;1.3.1&quot;&#xA;"
                priority="0">
                <range
                    file="../gradle/libs.versions.toml"
                    startOffset="25"
                    endOffset="25"/>
            </fix-replace>
            <fix-replace
                description="Replace with camera-camera2 = { module = &quot;androidx.camera:camera-camera2&quot;, version.ref = &quot;cameraCamera2Version&quot; }..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="camera-camera2 = { module = &quot;androidx.camera:camera-camera2&quot;, version.ref = &quot;cameraCamera2Version&quot; }&#xA;"
                priority="0">
                <range
                    file="../gradle/libs.versions.toml"
                    startOffset="312"
                    endOffset="312"/>
            </fix-replace>
            <fix-replace
                description="Replace with libs.camera.camera2"
                robot="true"
                replacement="libs.camera.camera2"
                priority="0">
                <range
                    file="${:app*projectDir}/build.gradle.kts"
                    startOffset="1129"
                    endOffset="1167"/>
            </fix-replace>
        </fix-composite>
        <location
            file="${:app*projectDir}/build.gradle.kts"
            line="49"
            column="20"
            startOffset="1129"
            endLine="49"
            endColumn="58"
            endOffset="1167"/>
    </incident>

    <incident
        id="UseTomlInstead"
        severity="warning"
        message="Use version catalog instead">
        <fix-composite
            description="Replace with new library catalog declaration for camera-lifecycle"
            robot="true">
            <fix-replace
                description="Replace with cameraLifecycleVersion = &quot;1.3.1&quot;..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="cameraLifecycleVersion = &quot;1.3.1&quot;&#xA;"
                priority="0">
                <range
                    file="../gradle/libs.versions.toml"
                    startOffset="25"
                    endOffset="25"/>
            </fix-replace>
            <fix-replace
                description="Replace with camera-lifecycle = { module = &quot;androidx.camera:camera-lifecycle&quot;, version.ref = &quot;cameraLifecycleVersion&quot; }..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="camera-lifecycle = { module = &quot;androidx.camera:camera-lifecycle&quot;, version.ref = &quot;cameraLifecycleVersion&quot; }&#xA;"
                priority="0">
                <range
                    file="../gradle/libs.versions.toml"
                    startOffset="312"
                    endOffset="312"/>
            </fix-replace>
            <fix-replace
                description="Replace with libs.camera.lifecycle"
                robot="true"
                replacement="libs.camera.lifecycle"
                priority="0">
                <range
                    file="${:app*projectDir}/build.gradle.kts"
                    startOffset="1188"
                    endOffset="1228"/>
            </fix-replace>
        </fix-composite>
        <location
            file="${:app*projectDir}/build.gradle.kts"
            line="50"
            column="20"
            startOffset="1188"
            endLine="50"
            endColumn="60"
            endOffset="1228"/>
    </incident>

    <incident
        id="UseTomlInstead"
        severity="warning"
        message="Use version catalog instead">
        <fix-composite
            description="Replace with new library catalog declaration for camera-view"
            robot="true">
            <fix-replace
                description="Replace with cameraViewVersion = &quot;1.3.1&quot;..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="cameraViewVersion = &quot;1.3.1&quot;&#xA;"
                priority="0">
                <range
                    file="../gradle/libs.versions.toml"
                    startOffset="25"
                    endOffset="25"/>
            </fix-replace>
            <fix-replace
                description="Replace with camera-view = { module = &quot;androidx.camera:camera-view&quot;, version.ref = &quot;cameraViewVersion&quot; }..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="camera-view = { module = &quot;androidx.camera:camera-view&quot;, version.ref = &quot;cameraViewVersion&quot; }&#xA;"
                priority="0">
                <range
                    file="../gradle/libs.versions.toml"
                    startOffset="312"
                    endOffset="312"/>
            </fix-replace>
            <fix-replace
                description="Replace with libs.camera.view"
                robot="true"
                replacement="libs.camera.view"
                priority="0">
                <range
                    file="${:app*projectDir}/build.gradle.kts"
                    startOffset="1249"
                    endOffset="1284"/>
            </fix-replace>
        </fix-composite>
        <location
            file="${:app*projectDir}/build.gradle.kts"
            line="51"
            column="20"
            startOffset="1249"
            endLine="51"
            endColumn="55"
            endOffset="1284"/>
    </incident>

    <incident
        id="UseTomlInstead"
        severity="warning"
        message="Use version catalog instead">
        <fix-composite
            description="Replace with new library catalog declaration for camera-extensions"
            robot="true">
            <fix-replace
                description="Replace with cameraExtensionsVersion = &quot;1.3.1&quot;..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="cameraExtensionsVersion = &quot;1.3.1&quot;&#xA;"
                priority="0">
                <range
                    file="../gradle/libs.versions.toml"
                    startOffset="25"
                    endOffset="25"/>
            </fix-replace>
            <fix-replace
                description="Replace with camera-extensions = { module = &quot;androidx.camera:camera-extensions&quot;, version.ref = &quot;cameraExtensionsVersion&quot; }..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="camera-extensions = { module = &quot;androidx.camera:camera-extensions&quot;, version.ref = &quot;cameraExtensionsVersion&quot; }&#xA;"
                priority="0">
                <range
                    file="../gradle/libs.versions.toml"
                    startOffset="312"
                    endOffset="312"/>
            </fix-replace>
            <fix-replace
                description="Replace with libs.camera.extensions"
                robot="true"
                replacement="libs.camera.extensions"
                priority="0">
                <range
                    file="${:app*projectDir}/build.gradle.kts"
                    startOffset="1305"
                    endOffset="1346"/>
            </fix-replace>
        </fix-composite>
        <location
            file="${:app*projectDir}/build.gradle.kts"
            line="52"
            column="20"
            startOffset="1305"
            endLine="52"
            endColumn="61"
            endOffset="1346"/>
    </incident>

</incidents>
