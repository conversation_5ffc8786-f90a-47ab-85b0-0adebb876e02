  Manifest android  CAMERA android.Manifest.permission  Activity android.app  ActivityResultContracts android.app.Activity  Boolean android.app.Activity  Box android.app.Activity  
CameraView android.app.Activity  
ContextCompat android.app.Activity  Face android.app.Activity  FaceOverlay android.app.Activity  	FaceTheme android.app.Activity  List android.app.Activity  Manifest android.app.Activity  Modifier android.app.Activity  PackageManager android.app.Activity  	emptyList android.app.Activity  fillMaxSize android.app.Activity  getValue android.app.Activity  mutableStateOf android.app.Activity  onCreate android.app.Activity  provideDelegate android.app.Activity  remember android.app.Activity  
setContent android.app.Activity  setValue android.app.Activity  Context android.content  ActivityResultContracts android.content.Context  Boolean android.content.Context  Box android.content.Context  
CameraView android.content.Context  
ContextCompat android.content.Context  Face android.content.Context  FaceOverlay android.content.Context  	FaceTheme android.content.Context  List android.content.Context  Manifest android.content.Context  Modifier android.content.Context  PackageManager android.content.Context  	emptyList android.content.Context  fillMaxSize android.content.Context  getValue android.content.Context  mutableStateOf android.content.Context  provideDelegate android.content.Context  remember android.content.Context  
setContent android.content.Context  setValue android.content.Context  ActivityResultContracts android.content.ContextWrapper  Boolean android.content.ContextWrapper  Box android.content.ContextWrapper  
CameraView android.content.ContextWrapper  
ContextCompat android.content.ContextWrapper  Face android.content.ContextWrapper  FaceOverlay android.content.ContextWrapper  	FaceTheme android.content.ContextWrapper  List android.content.ContextWrapper  Manifest android.content.ContextWrapper  Modifier android.content.ContextWrapper  PackageManager android.content.ContextWrapper  	emptyList android.content.ContextWrapper  fillMaxSize android.content.ContextWrapper  getValue android.content.ContextWrapper  mutableStateOf android.content.ContextWrapper  provideDelegate android.content.ContextWrapper  remember android.content.ContextWrapper  
setContent android.content.ContextWrapper  setValue android.content.ContextWrapper  PackageManager android.content.pm  PERMISSION_GRANTED !android.content.pm.PackageManager  PointF android.graphics  Rect android.graphics  x android.graphics.PointF  y android.graphics.PointF  bottom android.graphics.Rect  left android.graphics.Rect  right android.graphics.Rect  top android.graphics.Rect  Image 
android.media  Build 
android.os  Bundle 
android.os  SDK_INT android.os.Build.VERSION  S android.os.Build.VERSION_CODES  Log android.util  e android.util.Log  ActivityResultContracts  android.view.ContextThemeWrapper  Boolean  android.view.ContextThemeWrapper  Box  android.view.ContextThemeWrapper  
CameraView  android.view.ContextThemeWrapper  
ContextCompat  android.view.ContextThemeWrapper  Face  android.view.ContextThemeWrapper  FaceOverlay  android.view.ContextThemeWrapper  	FaceTheme  android.view.ContextThemeWrapper  List  android.view.ContextThemeWrapper  Manifest  android.view.ContextThemeWrapper  Modifier  android.view.ContextThemeWrapper  PackageManager  android.view.ContextThemeWrapper  	emptyList  android.view.ContextThemeWrapper  fillMaxSize  android.view.ContextThemeWrapper  getValue  android.view.ContextThemeWrapper  mutableStateOf  android.view.ContextThemeWrapper  provideDelegate  android.view.ContextThemeWrapper  remember  android.view.ContextThemeWrapper  
setContent  android.view.ContextThemeWrapper  setValue  android.view.ContextThemeWrapper  ComponentActivity androidx.activity  ActivityResultContracts #androidx.activity.ComponentActivity  Boolean #androidx.activity.ComponentActivity  Box #androidx.activity.ComponentActivity  
CameraView #androidx.activity.ComponentActivity  
ContextCompat #androidx.activity.ComponentActivity  Face #androidx.activity.ComponentActivity  FaceOverlay #androidx.activity.ComponentActivity  	FaceTheme #androidx.activity.ComponentActivity  List #androidx.activity.ComponentActivity  Manifest #androidx.activity.ComponentActivity  Modifier #androidx.activity.ComponentActivity  PackageManager #androidx.activity.ComponentActivity  	emptyList #androidx.activity.ComponentActivity  fillMaxSize #androidx.activity.ComponentActivity  getValue #androidx.activity.ComponentActivity  mutableStateOf #androidx.activity.ComponentActivity  onCreate #androidx.activity.ComponentActivity  provideDelegate #androidx.activity.ComponentActivity  registerForActivityResult #androidx.activity.ComponentActivity  remember #androidx.activity.ComponentActivity  
setContent #androidx.activity.ComponentActivity  setValue #androidx.activity.ComponentActivity  
setContent androidx.activity.compose  ActivityResultCallback androidx.activity.result  ActivityResultLauncher androidx.activity.result  <SAM-CONSTRUCTOR> /androidx.activity.result.ActivityResultCallback  launch /androidx.activity.result.ActivityResultLauncher  ActivityResultContracts !androidx.activity.result.contract  RequestPermission 9androidx.activity.result.contract.ActivityResultContracts  Camera androidx.camera.core  CameraSelector androidx.camera.core  ConcurrentCamera androidx.camera.core  
ImageAnalysis androidx.camera.core  	ImageInfo androidx.camera.core  
ImageProxy androidx.camera.core  Preview androidx.camera.core  DEFAULT_FRONT_CAMERA #androidx.camera.core.CameraSelector  Analyzer "androidx.camera.core.ImageAnalysis  Builder "androidx.camera.core.ImageAnalysis  STRATEGY_KEEP_ONLY_LATEST "androidx.camera.core.ImageAnalysis  also "androidx.camera.core.ImageAnalysis  setAnalyzer "androidx.camera.core.ImageAnalysis  <SAM-CONSTRUCTOR> +androidx.camera.core.ImageAnalysis.Analyzer  build *androidx.camera.core.ImageAnalysis.Builder  setBackpressureStrategy *androidx.camera.core.ImageAnalysis.Builder  rotationDegrees androidx.camera.core.ImageInfo  close androidx.camera.core.ImageProxy  image androidx.camera.core.ImageProxy  	imageInfo androidx.camera.core.ImageProxy  Builder androidx.camera.core.Preview  SurfaceProvider androidx.camera.core.Preview  also androidx.camera.core.Preview  setSurfaceProvider androidx.camera.core.Preview  build $androidx.camera.core.Preview.Builder  ProcessCameraProvider androidx.camera.lifecycle  bindToLifecycle /androidx.camera.lifecycle.ProcessCameraProvider  getInstance /androidx.camera.lifecycle.ProcessCameraProvider  	unbindAll /androidx.camera.lifecycle.ProcessCameraProvider  PreviewView androidx.camera.view  surfaceProvider  androidx.camera.view.PreviewView  Canvas androidx.compose.foundation  isSystemInDarkTheme androidx.compose.foundation  Box "androidx.compose.foundation.layout  BoxScope "androidx.compose.foundation.layout  fillMaxSize "androidx.compose.foundation.layout  
CameraView +androidx.compose.foundation.layout.BoxScope  FaceOverlay +androidx.compose.foundation.layout.BoxScope  ColorScheme androidx.compose.material3  
MaterialTheme androidx.compose.material3  
Typography androidx.compose.material3  darkColorScheme androidx.compose.material3  dynamicDarkColorScheme androidx.compose.material3  dynamicLightColorScheme androidx.compose.material3  lightColorScheme androidx.compose.material3  ActivityResultContracts androidx.compose.runtime  Boolean androidx.compose.runtime  Box androidx.compose.runtime  Bundle androidx.compose.runtime  CameraSelector androidx.compose.runtime  
CameraView androidx.compose.runtime  Color androidx.compose.runtime  ComponentActivity androidx.compose.runtime  
Composable androidx.compose.runtime  
ContextCompat androidx.compose.runtime  	Exception androidx.compose.runtime  	Executors androidx.compose.runtime  Face androidx.compose.runtime  
FaceDetection androidx.compose.runtime  FaceDetectorOptions androidx.compose.runtime  FaceOverlay androidx.compose.runtime  	FaceTheme androidx.compose.runtime  
ImageAnalysis androidx.compose.runtime  
InputImage androidx.compose.runtime  Int androidx.compose.runtime  List androidx.compose.runtime  Log androidx.compose.runtime  Manifest androidx.compose.runtime  Modifier androidx.compose.runtime  MutableState androidx.compose.runtime  Offset androidx.compose.runtime  PackageManager androidx.compose.runtime  Preview androidx.compose.runtime  ProcessCameraProvider androidx.compose.runtime  ProvidableCompositionLocal androidx.compose.runtime  Stroke androidx.compose.runtime  Unit androidx.compose.runtime  also androidx.compose.runtime  androidx androidx.compose.runtime  	emptyList androidx.compose.runtime  fillMaxSize androidx.compose.runtime  forEach androidx.compose.runtime  getValue androidx.compose.runtime  mutableStateOf androidx.compose.runtime  provideDelegate androidx.compose.runtime  remember androidx.compose.runtime  setValue androidx.compose.runtime  
getCurrent )androidx.compose.runtime.CompositionLocal  setValue %androidx.compose.runtime.MutableState  current 3androidx.compose.runtime.ProvidableCompositionLocal  ComposableFunction0 !androidx.compose.runtime.internal  ComposableFunction1 !androidx.compose.runtime.internal  Modifier androidx.compose.ui  	Companion androidx.compose.ui.Modifier  fillMaxSize androidx.compose.ui.Modifier  fillMaxSize &androidx.compose.ui.Modifier.Companion  Offset androidx.compose.ui.geometry  Size androidx.compose.ui.geometry  height !androidx.compose.ui.geometry.Size  width !androidx.compose.ui.geometry.Size  Color androidx.compose.ui.graphics  	Companion "androidx.compose.ui.graphics.Color  Red "androidx.compose.ui.graphics.Color  Yellow "androidx.compose.ui.graphics.Color  Red ,androidx.compose.ui.graphics.Color.Companion  Yellow ,androidx.compose.ui.graphics.Color.Companion  	DrawScope &androidx.compose.ui.graphics.drawscope  Stroke &androidx.compose.ui.graphics.drawscope  Color 0androidx.compose.ui.graphics.drawscope.DrawScope  Offset 0androidx.compose.ui.graphics.drawscope.DrawScope  Stroke 0androidx.compose.ui.graphics.drawscope.DrawScope  androidx 0androidx.compose.ui.graphics.drawscope.DrawScope  
drawCircle 0androidx.compose.ui.graphics.drawscope.DrawScope  drawRect 0androidx.compose.ui.graphics.drawscope.DrawScope  size 0androidx.compose.ui.graphics.drawscope.DrawScope  LocalContext androidx.compose.ui.platform  LocalLifecycleOwner androidx.compose.ui.platform  	TextStyle androidx.compose.ui.text  
FontFamily androidx.compose.ui.text.font  
FontWeight androidx.compose.ui.text.font  SystemFontFamily androidx.compose.ui.text.font  	Companion (androidx.compose.ui.text.font.FontFamily  Default (androidx.compose.ui.text.font.FontFamily  Default 2androidx.compose.ui.text.font.FontFamily.Companion  	Companion (androidx.compose.ui.text.font.FontWeight  Normal (androidx.compose.ui.text.font.FontWeight  Normal 2androidx.compose.ui.text.font.FontWeight.Companion  TextUnit androidx.compose.ui.unit  sp androidx.compose.ui.unit  AndroidView androidx.compose.ui.viewinterop  ActivityResultContracts #androidx.core.app.ComponentActivity  Boolean #androidx.core.app.ComponentActivity  Box #androidx.core.app.ComponentActivity  Bundle #androidx.core.app.ComponentActivity  
CameraView #androidx.core.app.ComponentActivity  
ContextCompat #androidx.core.app.ComponentActivity  Face #androidx.core.app.ComponentActivity  FaceOverlay #androidx.core.app.ComponentActivity  	FaceTheme #androidx.core.app.ComponentActivity  List #androidx.core.app.ComponentActivity  Manifest #androidx.core.app.ComponentActivity  Modifier #androidx.core.app.ComponentActivity  PackageManager #androidx.core.app.ComponentActivity  	emptyList #androidx.core.app.ComponentActivity  fillMaxSize #androidx.core.app.ComponentActivity  getValue #androidx.core.app.ComponentActivity  mutableStateOf #androidx.core.app.ComponentActivity  provideDelegate #androidx.core.app.ComponentActivity  remember #androidx.core.app.ComponentActivity  
setContent #androidx.core.app.ComponentActivity  setValue #androidx.core.app.ComponentActivity  
ContextCompat androidx.core.content  checkSelfPermission #androidx.core.content.ContextCompat  LifecycleOwner androidx.lifecycle  OnCompleteListener com.google.android.gms.tasks  OnFailureListener com.google.android.gms.tasks  OnSuccessListener com.google.android.gms.tasks  Task com.google.android.gms.tasks  <SAM-CONSTRUCTOR> /com.google.android.gms.tasks.OnCompleteListener  <SAM-CONSTRUCTOR> .com.google.android.gms.tasks.OnFailureListener  <SAM-CONSTRUCTOR> .com.google.android.gms.tasks.OnSuccessListener  addOnCompleteListener !com.google.android.gms.tasks.Task  addOnFailureListener !com.google.android.gms.tasks.Task  addOnSuccessListener !com.google.android.gms.tasks.Task  ListenableFuture !com.google.common.util.concurrent  get 2com.google.common.util.concurrent.ListenableFuture  
InputImage com.google.mlkit.vision.common  fromMediaImage )com.google.mlkit.vision.common.InputImage  height )com.google.mlkit.vision.common.InputImage  width )com.google.mlkit.vision.common.InputImage  Face com.google.mlkit.vision.face  
FaceDetection com.google.mlkit.vision.face  FaceDetector com.google.mlkit.vision.face  FaceDetectorOptions com.google.mlkit.vision.face  allLandmarks !com.google.mlkit.vision.face.Face  boundingBox !com.google.mlkit.vision.face.Face  	getClient *com.google.mlkit.vision.face.FaceDetection  process )com.google.mlkit.vision.face.FaceDetector  Builder 0com.google.mlkit.vision.face.FaceDetectorOptions  CLASSIFICATION_MODE_NONE 0com.google.mlkit.vision.face.FaceDetectorOptions  LANDMARK_MODE_ALL 0com.google.mlkit.vision.face.FaceDetectorOptions  PERFORMANCE_MODE_FAST 0com.google.mlkit.vision.face.FaceDetectorOptions  build 8com.google.mlkit.vision.face.FaceDetectorOptions.Builder  setClassificationMode 8com.google.mlkit.vision.face.FaceDetectorOptions.Builder  setLandmarkMode 8com.google.mlkit.vision.face.FaceDetectorOptions.Builder  setPerformanceMode 8com.google.mlkit.vision.face.FaceDetectorOptions.Builder  position )com.google.mlkit.vision.face.FaceLandmark  ActivityResultContracts com.wendy.face  Boolean com.wendy.face  Box com.wendy.face  Bundle com.wendy.face  CameraSelector com.wendy.face  
CameraView com.wendy.face  Color com.wendy.face  ComponentActivity com.wendy.face  
Composable com.wendy.face  
ContextCompat com.wendy.face  	Exception com.wendy.face  	Executors com.wendy.face  Face com.wendy.face  
FaceDetection com.wendy.face  FaceDetectorOptions com.wendy.face  FaceOverlay com.wendy.face  	FaceTheme com.wendy.face  
ImageAnalysis com.wendy.face  
InputImage com.wendy.face  Int com.wendy.face  List com.wendy.face  Log com.wendy.face  MainActivity com.wendy.face  Manifest com.wendy.face  Modifier com.wendy.face  Offset com.wendy.face  PackageManager com.wendy.face  Preview com.wendy.face  ProcessCameraProvider com.wendy.face  Stroke com.wendy.face  Unit com.wendy.face  also com.wendy.face  androidx com.wendy.face  	emptyList com.wendy.face  fillMaxSize com.wendy.face  forEach com.wendy.face  getValue com.wendy.face  mutableStateOf com.wendy.face  provideDelegate com.wendy.face  remember com.wendy.face  setValue com.wendy.face  ActivityResultContracts com.wendy.face.MainActivity  Box com.wendy.face.MainActivity  
CameraView com.wendy.face.MainActivity  
ContextCompat com.wendy.face.MainActivity  FaceOverlay com.wendy.face.MainActivity  	FaceTheme com.wendy.face.MainActivity  Manifest com.wendy.face.MainActivity  Modifier com.wendy.face.MainActivity  PackageManager com.wendy.face.MainActivity  	emptyList com.wendy.face.MainActivity  fillMaxSize com.wendy.face.MainActivity  getValue com.wendy.face.MainActivity  mutableStateOf com.wendy.face.MainActivity  provideDelegate com.wendy.face.MainActivity  registerForActivityResult com.wendy.face.MainActivity  remember com.wendy.face.MainActivity  requestPermissionLauncher com.wendy.face.MainActivity  
setContent com.wendy.face.MainActivity  setValue com.wendy.face.MainActivity  Boolean com.wendy.face.ui.theme  Build com.wendy.face.ui.theme  
Composable com.wendy.face.ui.theme  DarkColorScheme com.wendy.face.ui.theme  	FaceTheme com.wendy.face.ui.theme  
FontFamily com.wendy.face.ui.theme  
FontWeight com.wendy.face.ui.theme  LightColorScheme com.wendy.face.ui.theme  Pink40 com.wendy.face.ui.theme  Pink80 com.wendy.face.ui.theme  Purple40 com.wendy.face.ui.theme  Purple80 com.wendy.face.ui.theme  PurpleGrey40 com.wendy.face.ui.theme  PurpleGrey80 com.wendy.face.ui.theme  
Typography com.wendy.face.ui.theme  Unit com.wendy.face.ui.theme  	Exception 	java.lang  	Executors java.util.concurrent  newSingleThreadExecutor java.util.concurrent.Executors  	Function0 kotlin  	Function1 kotlin  	Function3 kotlin  Nothing kotlin  also kotlin  sp 
kotlin.Double  div kotlin.Float  minus kotlin.Float  times kotlin.Float  invoke kotlin.Function3  	compareTo 
kotlin.Int  times 
kotlin.Int  toFloat 
kotlin.Int  Iterator kotlin.collections  List kotlin.collections  	emptyList kotlin.collections  forEach kotlin.collections  hasNext kotlin.collections.Iterator  next kotlin.collections.Iterator  iterator kotlin.collections.List  KMutableProperty0 kotlin.reflect  forEach kotlin.sequences  forEach kotlin.text  WRITE_EXTERNAL_STORAGE android.Manifest.permission  android android.app.Activity  
ContentValues android.content  android android.content.ContentValues  apply android.content.ContentValues  put android.content.ContentValues  android android.content.Context  contentResolver android.content.Context  android android.content.ContextWrapper  Uri android.net  let android.net.Uri  P android.os.Build.VERSION_CODES  EXTERNAL_CONTENT_URI (android.provider.MediaStore.Images.Media  
RELATIVE_PATH (android.provider.MediaStore.Images.Media  DISPLAY_NAME (android.provider.MediaStore.MediaColumns  	MIME_TYPE (android.provider.MediaStore.MediaColumns  
RELATIVE_PATH (android.provider.MediaStore.MediaColumns  d android.util.Log  android  android.view.ContextThemeWrapper  android #androidx.activity.ComponentActivity  ImageCapture androidx.camera.core  ImageCaptureException androidx.camera.core  DEFAULT_BACK_CAMERA #androidx.camera.core.CameraSelector  Builder !androidx.camera.core.ImageCapture  CAPTURE_MODE_MAXIMIZE_QUALITY !androidx.camera.core.ImageCapture  OnImageSavedCallback !androidx.camera.core.ImageCapture  OutputFileOptions !androidx.camera.core.ImageCapture  OutputFileResults !androidx.camera.core.ImageCapture  let !androidx.camera.core.ImageCapture  takePicture !androidx.camera.core.ImageCapture  build )androidx.camera.core.ImageCapture.Builder  setCaptureMode )androidx.camera.core.ImageCapture.Builder  Builder 3androidx.camera.core.ImageCapture.OutputFileOptions  build ;androidx.camera.core.ImageCapture.OutputFileOptions.Builder  savedUri 3androidx.camera.core.ImageCapture.OutputFileResults  message *androidx.camera.core.ImageCaptureException  Arrangement "androidx.compose.foundation.layout  Column "androidx.compose.foundation.layout  Row "androidx.compose.foundation.layout  RowScope "androidx.compose.foundation.layout  fillMaxWidth "androidx.compose.foundation.layout  padding "androidx.compose.foundation.layout  size "androidx.compose.foundation.layout  HorizontalOrVertical .androidx.compose.foundation.layout.Arrangement  SpaceEvenly .androidx.compose.foundation.layout.Arrangement  AndroidView +androidx.compose.foundation.layout.BoxScope  Arrangement +androidx.compose.foundation.layout.BoxScope  Button +androidx.compose.foundation.layout.BoxScope  CameraSelector +androidx.compose.foundation.layout.BoxScope  ImageCapture +androidx.compose.foundation.layout.BoxScope  Log +androidx.compose.foundation.layout.BoxScope  Modifier +androidx.compose.foundation.layout.BoxScope  Preview +androidx.compose.foundation.layout.BoxScope  PreviewView +androidx.compose.foundation.layout.BoxScope  Row +androidx.compose.foundation.layout.BoxScope  Text +androidx.compose.foundation.layout.BoxScope  align +androidx.compose.foundation.layout.BoxScope  also +androidx.compose.foundation.layout.BoxScope  androidx +androidx.compose.foundation.layout.BoxScope  dp +androidx.compose.foundation.layout.BoxScope  fillMaxSize +androidx.compose.foundation.layout.BoxScope  fillMaxWidth +androidx.compose.foundation.layout.BoxScope  let +androidx.compose.foundation.layout.BoxScope  padding +androidx.compose.foundation.layout.BoxScope  takePicture +androidx.compose.foundation.layout.BoxScope  Button +androidx.compose.foundation.layout.RowScope  Text +androidx.compose.foundation.layout.RowScope  let +androidx.compose.foundation.layout.RowScope  takePicture +androidx.compose.foundation.layout.RowScope  Button androidx.compose.material3  FloatingActionButton androidx.compose.material3  Text androidx.compose.material3  AndroidView androidx.compose.runtime  Arrangement androidx.compose.runtime  Button androidx.compose.runtime  	DrawScope androidx.compose.runtime  FaceContour androidx.compose.runtime  Float androidx.compose.runtime  ImageCapture androidx.compose.runtime  ImageCaptureException androidx.compose.runtime  Locale androidx.compose.runtime  Path androidx.compose.runtime  PreviewView androidx.compose.runtime  Row androidx.compose.runtime  System androidx.compose.runtime  Text androidx.compose.runtime  android androidx.compose.runtime  apply androidx.compose.runtime  com androidx.compose.runtime  fillMaxWidth androidx.compose.runtime  let androidx.compose.runtime  listOf androidx.compose.runtime  padding androidx.compose.runtime  takePicture androidx.compose.runtime  until androidx.compose.runtime  OnImageSavedCallback %androidx.compose.runtime.ImageCapture  OutputFileResults %androidx.compose.runtime.ImageCapture  content  androidx.compose.runtime.android  Context (androidx.compose.runtime.android.content  google androidx.compose.runtime.com  mlkit #androidx.compose.runtime.com.google  vision )androidx.compose.runtime.com.google.mlkit  face 0androidx.compose.runtime.com.google.mlkit.vision  FaceDetector 5androidx.compose.runtime.com.google.mlkit.vision.face  	Alignment androidx.compose.ui  BottomCenter androidx.compose.ui.Alignment  	Companion androidx.compose.ui.Alignment  BottomCenter 'androidx.compose.ui.Alignment.Companion  align androidx.compose.ui.Modifier  fillMaxWidth androidx.compose.ui.Modifier  padding androidx.compose.ui.Modifier  fillMaxWidth &androidx.compose.ui.Modifier.Companion  Path androidx.compose.ui.graphics  Blue "androidx.compose.ui.graphics.Color  Cyan "androidx.compose.ui.graphics.Color  Green "androidx.compose.ui.graphics.Color  Magenta "androidx.compose.ui.graphics.Color  White "androidx.compose.ui.graphics.Color  Blue ,androidx.compose.ui.graphics.Color.Companion  Cyan ,androidx.compose.ui.graphics.Color.Companion  Green ,androidx.compose.ui.graphics.Color.Companion  Magenta ,androidx.compose.ui.graphics.Color.Companion  White ,androidx.compose.ui.graphics.Color.Companion  close !androidx.compose.ui.graphics.Path  lineTo !androidx.compose.ui.graphics.Path  moveTo !androidx.compose.ui.graphics.Path  FaceContour 0androidx.compose.ui.graphics.drawscope.DrawScope  Path 0androidx.compose.ui.graphics.drawscope.DrawScope  com 0androidx.compose.ui.graphics.drawscope.DrawScope  drawFaceContours 0androidx.compose.ui.graphics.drawscope.DrawScope  drawPath 0androidx.compose.ui.graphics.drawscope.DrawScope  let 0androidx.compose.ui.graphics.drawscope.DrawScope  listOf 0androidx.compose.ui.graphics.drawscope.DrawScope  until 0androidx.compose.ui.graphics.drawscope.DrawScope  Dp androidx.compose.ui.unit  dp androidx.compose.ui.unit  getDp androidx.compose.ui.unit  android #androidx.core.app.ComponentActivity  getMainExecutor #androidx.core.content.ContextCompat  fromFilePath )com.google.mlkit.vision.common.InputImage  FaceContour com.google.mlkit.vision.face  
getContour !com.google.mlkit.vision.face.Face  FACE (com.google.mlkit.vision.face.FaceContour  LEFT_EYE (com.google.mlkit.vision.face.FaceContour  LEFT_EYEBROW_BOTTOM (com.google.mlkit.vision.face.FaceContour  LEFT_EYEBROW_TOP (com.google.mlkit.vision.face.FaceContour  LOWER_LIP_BOTTOM (com.google.mlkit.vision.face.FaceContour  
LOWER_LIP_TOP (com.google.mlkit.vision.face.FaceContour  NOSE_BOTTOM (com.google.mlkit.vision.face.FaceContour  NOSE_BRIDGE (com.google.mlkit.vision.face.FaceContour  	RIGHT_EYE (com.google.mlkit.vision.face.FaceContour  RIGHT_EYEBROW_BOTTOM (com.google.mlkit.vision.face.FaceContour  RIGHT_EYEBROW_TOP (com.google.mlkit.vision.face.FaceContour  UPPER_LIP_BOTTOM (com.google.mlkit.vision.face.FaceContour  
UPPER_LIP_TOP (com.google.mlkit.vision.face.FaceContour  let (com.google.mlkit.vision.face.FaceContour  points (com.google.mlkit.vision.face.FaceContour  CLASSIFICATION_MODE_ALL 0com.google.mlkit.vision.face.FaceDetectorOptions  CONTOUR_MODE_ALL 0com.google.mlkit.vision.face.FaceDetectorOptions  PERFORMANCE_MODE_ACCURATE 0com.google.mlkit.vision.face.FaceDetectorOptions  enableTracking 8com.google.mlkit.vision.face.FaceDetectorOptions.Builder  setContourMode 8com.google.mlkit.vision.face.FaceDetectorOptions.Builder  setMinFaceSize 8com.google.mlkit.vision.face.FaceDetectorOptions.Builder  LEFT_EAR )com.google.mlkit.vision.face.FaceLandmark  LEFT_EYE )com.google.mlkit.vision.face.FaceLandmark  MOUTH_BOTTOM )com.google.mlkit.vision.face.FaceLandmark  
MOUTH_LEFT )com.google.mlkit.vision.face.FaceLandmark  MOUTH_RIGHT )com.google.mlkit.vision.face.FaceLandmark  	NOSE_BASE )com.google.mlkit.vision.face.FaceLandmark  	RIGHT_EAR )com.google.mlkit.vision.face.FaceLandmark  	RIGHT_EYE )com.google.mlkit.vision.face.FaceLandmark  landmarkType )com.google.mlkit.vision.face.FaceLandmark  AndroidView com.wendy.face  Arrangement com.wendy.face  Button com.wendy.face  	DrawScope com.wendy.face  FaceContour com.wendy.face  Float com.wendy.face  ImageCapture com.wendy.face  ImageCaptureException com.wendy.face  Locale com.wendy.face  Path com.wendy.face  PreviewView com.wendy.face  Row com.wendy.face  System com.wendy.face  Text com.wendy.face  android com.wendy.face  apply com.wendy.face  com com.wendy.face  drawFaceContours com.wendy.face  fillMaxWidth com.wendy.face  let com.wendy.face  listOf com.wendy.face  padding com.wendy.face  takePicture com.wendy.face  until com.wendy.face  OnImageSavedCallback com.wendy.face.ImageCapture  OutputFileResults com.wendy.face.ImageCapture  android com.wendy.face.MainActivity  content com.wendy.face.android  Context com.wendy.face.android.content  google com.wendy.face.com  mlkit com.wendy.face.com.google  vision com.wendy.face.com.google.mlkit  face &com.wendy.face.com.google.mlkit.vision  FaceDetector +com.wendy.face.com.google.mlkit.vision.face  File java.io  currentTimeMillis java.lang.System  SimpleDateFormat 	java.text  format java.text.DateFormat  format java.text.Format  format java.text.SimpleDateFormat  Locale 	java.util  US java.util.Locale  Executor java.util.concurrent  apply kotlin  let kotlin  not kotlin.Boolean  message kotlin.Throwable  IntIterator kotlin.collections  listOf kotlin.collections  hasNext kotlin.collections.IntIterator  next kotlin.collections.IntIterator  get kotlin.collections.MutableList  size kotlin.collections.MutableList  	CharRange 
kotlin.ranges  IntRange 
kotlin.ranges  	LongRange 
kotlin.ranges  	UIntRange 
kotlin.ranges  
ULongRange 
kotlin.ranges  until 
kotlin.ranges  iterator kotlin.ranges.IntProgression  iterator kotlin.ranges.IntRange  addListener 2com.google.common.util.concurrent.ListenableFuture  Runnable 	java.lang  <SAM-CONSTRUCTOR> java.lang.Runnable  Log android.app.Activity  String android.app.Activity  
isNotEmpty android.app.Activity  
mutableListOf android.app.Activity  toTypedArray android.app.Activity  Log android.content.Context  String android.content.Context  
isNotEmpty android.content.Context  
mutableListOf android.content.Context  toTypedArray android.content.Context  Log android.content.ContextWrapper  String android.content.ContextWrapper  
isNotEmpty android.content.ContextWrapper  
mutableListOf android.content.ContextWrapper  toTypedArray android.content.ContextWrapper  w android.util.Log  Log  android.view.ContextThemeWrapper  String  android.view.ContextThemeWrapper  
isNotEmpty  android.view.ContextThemeWrapper  
mutableListOf  android.view.ContextThemeWrapper  toTypedArray  android.view.ContextThemeWrapper  Log #androidx.activity.ComponentActivity  String #androidx.activity.ComponentActivity  
isNotEmpty #androidx.activity.ComponentActivity  
mutableListOf #androidx.activity.ComponentActivity  toTypedArray #androidx.activity.ComponentActivity  RequestMultiplePermissions 9androidx.activity.result.contract.ActivityResultContracts  Log +androidx.compose.foundation.layout.RowScope  String androidx.compose.runtime  
isNotEmpty androidx.compose.runtime  
mutableListOf androidx.compose.runtime  toTypedArray androidx.compose.runtime  Log #androidx.core.app.ComponentActivity  String #androidx.core.app.ComponentActivity  
isNotEmpty #androidx.core.app.ComponentActivity  
mutableListOf #androidx.core.app.ComponentActivity  toTypedArray #androidx.core.app.ComponentActivity  String com.wendy.face  
isNotEmpty com.wendy.face  
mutableListOf com.wendy.face  toTypedArray com.wendy.face  Log com.wendy.face.MainActivity  
isNotEmpty com.wendy.face.MainActivity  
mutableListOf com.wendy.face.MainActivity  "requestMultiplePermissionsLauncher com.wendy.face.MainActivity  toTypedArray com.wendy.face.MainActivity  Array kotlin  invoke kotlin.Function0  MutableList kotlin.collections  Set kotlin.collections  
isNotEmpty kotlin.collections  
mutableListOf kotlin.collections  toTypedArray kotlin.collections  Entry kotlin.collections.Map  entries kotlin.collections.Map  key kotlin.collections.Map.Entry  value kotlin.collections.Map.Entry  add kotlin.collections.MutableList  
isNotEmpty kotlin.collections.MutableList  toTypedArray kotlin.collections.MutableList  
isNotEmpty kotlin.text  let  androidx.camera.view.PreviewView  LaunchedEffect androidx.compose.runtime  LaunchedEffect com.wendy.face  SuspendFunction1 kotlin.coroutines  CoroutineScope kotlinx.coroutines  CameraSelector !kotlinx.coroutines.CoroutineScope  ImageCapture !kotlinx.coroutines.CoroutineScope  Log !kotlinx.coroutines.CoroutineScope  Preview !kotlinx.coroutines.CoroutineScope  also !kotlinx.coroutines.CoroutineScope  androidx !kotlinx.coroutines.CoroutineScope  let !kotlinx.coroutines.CoroutineScope  SimpleCameraPreview android.app.Activity  SimpleCameraPreview android.content.Context  SimpleCameraPreview android.content.ContextWrapper  SimpleCameraPreview  android.view.ContextThemeWrapper  SimpleCameraPreview #androidx.activity.ComponentActivity  
ContextCompat +androidx.compose.foundation.layout.BoxScope  SimpleCameraPreview androidx.compose.runtime  SimpleCameraPreview #androidx.core.app.ComponentActivity  SimpleCameraPreview com.wendy.face  TestCameraActivity com.wendy.face  ActivityResultContracts !com.wendy.face.TestCameraActivity  
ContextCompat !com.wendy.face.TestCameraActivity  	FaceTheme !com.wendy.face.TestCameraActivity  Log !com.wendy.face.TestCameraActivity  Manifest !com.wendy.face.TestCameraActivity  PackageManager !com.wendy.face.TestCameraActivity  SimpleCameraPreview !com.wendy.face.TestCameraActivity  registerForActivityResult !com.wendy.face.TestCameraActivity  requestPermissionLauncher !com.wendy.face.TestCameraActivity  
setContent !com.wendy.face.TestCameraActivity  Bitmap android.app.Activity  Button android.app.Activity  ContentScale android.app.Activity  Image android.app.Activity  Text android.app.Activity  Uri android.app.Activity  align android.app.Activity  androidx android.app.Activity  
asImageBitmap android.app.Activity  dp android.app.Activity  let android.app.Activity  padding android.app.Activity  openInputStream android.content.ContentResolver  Bitmap android.content.Context  Button android.content.Context  ContentScale android.content.Context  Image android.content.Context  Text android.content.Context  Uri android.content.Context  align android.content.Context  androidx android.content.Context  
asImageBitmap android.content.Context  dp android.content.Context  let android.content.Context  padding android.content.Context  Bitmap android.content.ContextWrapper  Button android.content.ContextWrapper  ContentScale android.content.ContextWrapper  Image android.content.ContextWrapper  Text android.content.ContextWrapper  Uri android.content.ContextWrapper  align android.content.ContextWrapper  androidx android.content.ContextWrapper  
asImageBitmap android.content.ContextWrapper  dp android.content.ContextWrapper  let android.content.ContextWrapper  padding android.content.ContextWrapper  Bitmap android.graphics  
BitmapFactory android.graphics  
asImageBitmap android.graphics.Bitmap  height android.graphics.Bitmap  let android.graphics.Bitmap  width android.graphics.Bitmap  decodeStream android.graphics.BitmapFactory  Bitmap  android.view.ContextThemeWrapper  Button  android.view.ContextThemeWrapper  ContentScale  android.view.ContextThemeWrapper  Image  android.view.ContextThemeWrapper  Text  android.view.ContextThemeWrapper  Uri  android.view.ContextThemeWrapper  align  android.view.ContextThemeWrapper  androidx  android.view.ContextThemeWrapper  
asImageBitmap  android.view.ContextThemeWrapper  dp  android.view.ContextThemeWrapper  let  android.view.ContextThemeWrapper  padding  android.view.ContextThemeWrapper  Bitmap #androidx.activity.ComponentActivity  Button #androidx.activity.ComponentActivity  ContentScale #androidx.activity.ComponentActivity  Image #androidx.activity.ComponentActivity  Text #androidx.activity.ComponentActivity  Uri #androidx.activity.ComponentActivity  align #androidx.activity.ComponentActivity  androidx #androidx.activity.ComponentActivity  
asImageBitmap #androidx.activity.ComponentActivity  dp #androidx.activity.ComponentActivity  let #androidx.activity.ComponentActivity  padding #androidx.activity.ComponentActivity  Image androidx.compose.foundation  ContentScale +androidx.compose.foundation.layout.BoxScope  Image +androidx.compose.foundation.layout.BoxScope  
asImageBitmap +androidx.compose.foundation.layout.BoxScope  	emptyList +androidx.compose.foundation.layout.BoxScope  Bitmap androidx.compose.runtime  
BitmapFactory androidx.compose.runtime  ContentScale androidx.compose.runtime  Image androidx.compose.runtime  Uri androidx.compose.runtime  align androidx.compose.runtime  arrayOf androidx.compose.runtime  
asImageBitmap androidx.compose.runtime  
component1 androidx.compose.runtime  
component2 androidx.compose.runtime  
component3 androidx.compose.runtime  
component4 androidx.compose.runtime  TopEnd androidx.compose.ui.Alignment  TopEnd 'androidx.compose.ui.Alignment.Companion  align &androidx.compose.ui.Modifier.Companion  ImageBitmap androidx.compose.ui.graphics  
asImageBitmap androidx.compose.ui.graphics  arrayOf 0androidx.compose.ui.graphics.drawscope.DrawScope  
component1 0androidx.compose.ui.graphics.drawscope.DrawScope  
component2 0androidx.compose.ui.graphics.drawscope.DrawScope  
component3 0androidx.compose.ui.graphics.drawscope.DrawScope  
component4 0androidx.compose.ui.graphics.drawscope.DrawScope  ContentScale androidx.compose.ui.layout  	Companion 'androidx.compose.ui.layout.ContentScale  Fit 'androidx.compose.ui.layout.ContentScale  Fit 1androidx.compose.ui.layout.ContentScale.Companion  Bitmap #androidx.core.app.ComponentActivity  Button #androidx.core.app.ComponentActivity  ContentScale #androidx.core.app.ComponentActivity  Image #androidx.core.app.ComponentActivity  Text #androidx.core.app.ComponentActivity  Uri #androidx.core.app.ComponentActivity  align #androidx.core.app.ComponentActivity  androidx #androidx.core.app.ComponentActivity  
asImageBitmap #androidx.core.app.ComponentActivity  dp #androidx.core.app.ComponentActivity  let #androidx.core.app.ComponentActivity  padding #androidx.core.app.ComponentActivity  
fromBitmap )com.google.mlkit.vision.common.InputImage  Bitmap com.wendy.face  
BitmapFactory com.wendy.face  ContentScale com.wendy.face  Image com.wendy.face  Uri com.wendy.face  align com.wendy.face  arrayOf com.wendy.face  
asImageBitmap com.wendy.face  
component1 com.wendy.face  
component2 com.wendy.face  
component3 com.wendy.face  
component4 com.wendy.face  Button com.wendy.face.MainActivity  ContentScale com.wendy.face.MainActivity  Image com.wendy.face.MainActivity  Text com.wendy.face.MainActivity  align com.wendy.face.MainActivity  androidx com.wendy.face.MainActivity  
asImageBitmap com.wendy.face.MainActivity  dp com.wendy.face.MainActivity  let com.wendy.face.MainActivity  padding com.wendy.face.MainActivity  InputStream java.io  close java.io.InputStream  	Function2 kotlin  arrayOf kotlin  
component1 kotlin.Array  
component2 kotlin.Array  
component3 kotlin.Array  
component4 kotlin.Array  	compareTo kotlin.Float  plus kotlin.Float  invoke kotlin.Function2  
component1 kotlin.collections  
component2 kotlin.collections  
component3 kotlin.collections  
component4 kotlin.collections  isEmpty kotlin.collections.List  forEachIndexed androidx.compose.runtime  Log 0androidx.compose.ui.graphics.drawscope.DrawScope  forEachIndexed com.wendy.face  forEachIndexed kotlin.collections  forEachIndexed kotlin.sequences  forEachIndexed kotlin.text  Arrangement android.app.Activity  Row android.app.Activity  spacedBy android.app.Activity  testFaceDetection android.app.Activity  Arrangement android.content.Context  Row android.content.Context  spacedBy android.content.Context  testFaceDetection android.content.Context  Arrangement android.content.ContextWrapper  Row android.content.ContextWrapper  spacedBy android.content.ContextWrapper  testFaceDetection android.content.ContextWrapper  Canvas android.graphics  Paint android.graphics  Config android.graphics.Bitmap  createBitmap android.graphics.Bitmap  
eraseColor android.graphics.Bitmap  	ARGB_8888 android.graphics.Bitmap.Config  
drawCircle android.graphics.Canvas  drawRect android.graphics.Canvas  BLUE android.graphics.Color  RED android.graphics.Color  YELLOW android.graphics.Color  Style android.graphics.Paint  android android.graphics.Paint  apply android.graphics.Paint  color android.graphics.Paint  strokeWidth android.graphics.Paint  style android.graphics.Paint  FILL android.graphics.Paint.Style  STROKE android.graphics.Paint.Style  Arrangement  android.view.ContextThemeWrapper  Row  android.view.ContextThemeWrapper  spacedBy  android.view.ContextThemeWrapper  testFaceDetection  android.view.ContextThemeWrapper  Arrangement #androidx.activity.ComponentActivity  Row #androidx.activity.ComponentActivity  spacedBy #androidx.activity.ComponentActivity  testFaceDetection #androidx.activity.ComponentActivity  
Horizontal .androidx.compose.foundation.layout.Arrangement  Vertical .androidx.compose.foundation.layout.Arrangement  spacedBy .androidx.compose.foundation.layout.Arrangement  spacedBy +androidx.compose.foundation.layout.BoxScope  testFaceDetection +androidx.compose.foundation.layout.BoxScope  	emptyList +androidx.compose.foundation.layout.RowScope  testFaceDetection +androidx.compose.foundation.layout.RowScope  spacedBy androidx.compose.runtime  testFaceDetection androidx.compose.runtime  Arrangement #androidx.core.app.ComponentActivity  Row #androidx.core.app.ComponentActivity  spacedBy #androidx.core.app.ComponentActivity  testFaceDetection #androidx.core.app.ComponentActivity  spacedBy com.wendy.face  testFaceDetection com.wendy.face  Arrangement com.wendy.face.MainActivity  Row com.wendy.face.MainActivity  spacedBy com.wendy.face.MainActivity  testFaceDetection com.wendy.face.MainActivity  	Function4 kotlin  invoke kotlin.Function4                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                