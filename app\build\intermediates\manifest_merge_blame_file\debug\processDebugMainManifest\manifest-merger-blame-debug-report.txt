1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.wendy.face"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="28"
9        android:targetSdkVersion="35" />
10
11    <uses-feature android:name="android.hardware.camera.any" />
11-->D:\workspace\gitee.com\wendy\face\app\src\main\AndroidManifest.xml:5:5-64
11-->D:\workspace\gitee.com\wendy\face\app\src\main\AndroidManifest.xml:5:19-61
12
13    <uses-permission android:name="android.permission.CAMERA" />
13-->D:\workspace\gitee.com\wendy\face\app\src\main\AndroidManifest.xml:6:5-65
13-->D:\workspace\gitee.com\wendy\face\app\src\main\AndroidManifest.xml:6:22-62
14    <uses-permission
14-->D:\workspace\gitee.com\wendy\face\app\src\main\AndroidManifest.xml:7:5-8:38
15        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
15-->D:\workspace\gitee.com\wendy\face\app\src\main\AndroidManifest.xml:7:22-78
16        android:maxSdkVersion="28" />
16-->D:\workspace\gitee.com\wendy\face\app\src\main\AndroidManifest.xml:8:9-35
17    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
17-->D:\workspace\gitee.com\wendy\face\app\src\main\AndroidManifest.xml:9:5-80
17-->D:\workspace\gitee.com\wendy\face\app\src\main\AndroidManifest.xml:9:22-77
18
19    <queries>
19-->[androidx.camera:camera-extensions:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7174d9c888ae98529a7d8745a19334b9\transformed\camera-extensions-1.3.1\AndroidManifest.xml:22:5-26:15
20        <intent>
20-->[androidx.camera:camera-extensions:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7174d9c888ae98529a7d8745a19334b9\transformed\camera-extensions-1.3.1\AndroidManifest.xml:23:9-25:18
21            <action android:name="androidx.camera.extensions.action.VENDOR_ACTION" />
21-->[androidx.camera:camera-extensions:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7174d9c888ae98529a7d8745a19334b9\transformed\camera-extensions-1.3.1\AndroidManifest.xml:24:13-86
21-->[androidx.camera:camera-extensions:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7174d9c888ae98529a7d8745a19334b9\transformed\camera-extensions-1.3.1\AndroidManifest.xml:24:21-83
22        </intent>
23    </queries>
24
25    <permission
25-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\afde12078f7f3fef585f13cd9d4f1674\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
26        android:name="com.wendy.face.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
26-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\afde12078f7f3fef585f13cd9d4f1674\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
27        android:protectionLevel="signature" />
27-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\afde12078f7f3fef585f13cd9d4f1674\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
28
29    <uses-permission android:name="com.wendy.face.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" /> <!-- Although the *SdkVersion is captured in gradle build files, this is required for non gradle builds -->
29-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\afde12078f7f3fef585f13cd9d4f1674\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
29-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\afde12078f7f3fef585f13cd9d4f1674\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
30    <!-- <uses-sdk android:minSdkVersion="14"/> -->
31    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
31-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\62f67fda4d973d784ed6d16c274509a5\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:25:5-79
31-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\62f67fda4d973d784ed6d16c274509a5\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:25:22-76
32    <uses-permission android:name="android.permission.INTERNET" />
32-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\62f67fda4d973d784ed6d16c274509a5\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:26:5-67
32-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\62f67fda4d973d784ed6d16c274509a5\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:26:22-64
33
34    <application
34-->D:\workspace\gitee.com\wendy\face\app\src\main\AndroidManifest.xml:11:5-32:19
35        android:allowBackup="true"
35-->D:\workspace\gitee.com\wendy\face\app\src\main\AndroidManifest.xml:12:9-35
36        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
36-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\afde12078f7f3fef585f13cd9d4f1674\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
37        android:dataExtractionRules="@xml/data_extraction_rules"
37-->D:\workspace\gitee.com\wendy\face\app\src\main\AndroidManifest.xml:13:9-65
38        android:debuggable="true"
39        android:extractNativeLibs="false"
40        android:fullBackupContent="@xml/backup_rules"
40-->D:\workspace\gitee.com\wendy\face\app\src\main\AndroidManifest.xml:14:9-54
41        android:icon="@mipmap/ic_launcher"
41-->D:\workspace\gitee.com\wendy\face\app\src\main\AndroidManifest.xml:15:9-43
42        android:label="@string/app_name"
42-->D:\workspace\gitee.com\wendy\face\app\src\main\AndroidManifest.xml:16:9-41
43        android:roundIcon="@mipmap/ic_launcher_round"
43-->D:\workspace\gitee.com\wendy\face\app\src\main\AndroidManifest.xml:17:9-54
44        android:supportsRtl="true"
44-->D:\workspace\gitee.com\wendy\face\app\src\main\AndroidManifest.xml:18:9-35
45        android:theme="@style/Theme.Face" >
45-->D:\workspace\gitee.com\wendy\face\app\src\main\AndroidManifest.xml:19:9-42
46        <activity
46-->D:\workspace\gitee.com\wendy\face\app\src\main\AndroidManifest.xml:21:9-31:20
47            android:name="com.wendy.face.MainActivity"
47-->D:\workspace\gitee.com\wendy\face\app\src\main\AndroidManifest.xml:22:13-41
48            android:exported="true"
48-->D:\workspace\gitee.com\wendy\face\app\src\main\AndroidManifest.xml:23:13-36
49            android:label="@string/app_name"
49-->D:\workspace\gitee.com\wendy\face\app\src\main\AndroidManifest.xml:24:13-45
50            android:theme="@style/Theme.Face" >
50-->D:\workspace\gitee.com\wendy\face\app\src\main\AndroidManifest.xml:25:13-46
51            <intent-filter>
51-->D:\workspace\gitee.com\wendy\face\app\src\main\AndroidManifest.xml:26:13-30:29
52                <action android:name="android.intent.action.MAIN" />
52-->D:\workspace\gitee.com\wendy\face\app\src\main\AndroidManifest.xml:27:17-69
52-->D:\workspace\gitee.com\wendy\face\app\src\main\AndroidManifest.xml:27:25-66
53
54                <category android:name="android.intent.category.LAUNCHER" />
54-->D:\workspace\gitee.com\wendy\face\app\src\main\AndroidManifest.xml:29:17-77
54-->D:\workspace\gitee.com\wendy\face\app\src\main\AndroidManifest.xml:29:27-74
55            </intent-filter>
56        </activity>
57
58        <uses-library
58-->[androidx.camera:camera-extensions:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7174d9c888ae98529a7d8745a19334b9\transformed\camera-extensions-1.3.1\AndroidManifest.xml:29:9-31:40
59            android:name="androidx.camera.extensions.impl"
59-->[androidx.camera:camera-extensions:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7174d9c888ae98529a7d8745a19334b9\transformed\camera-extensions-1.3.1\AndroidManifest.xml:30:13-59
60            android:required="false" />
60-->[androidx.camera:camera-extensions:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7174d9c888ae98529a7d8745a19334b9\transformed\camera-extensions-1.3.1\AndroidManifest.xml:31:13-37
61
62        <service
62-->[androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\39959a6f52757fe9191c79dc0e003492\transformed\camera-camera2-1.3.1\AndroidManifest.xml:24:9-33:19
63            android:name="androidx.camera.core.impl.MetadataHolderService"
63-->[androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\39959a6f52757fe9191c79dc0e003492\transformed\camera-camera2-1.3.1\AndroidManifest.xml:25:13-75
64            android:enabled="false"
64-->[androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\39959a6f52757fe9191c79dc0e003492\transformed\camera-camera2-1.3.1\AndroidManifest.xml:26:13-36
65            android:exported="false" >
65-->[androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\39959a6f52757fe9191c79dc0e003492\transformed\camera-camera2-1.3.1\AndroidManifest.xml:27:13-37
66            <meta-data
66-->[androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\39959a6f52757fe9191c79dc0e003492\transformed\camera-camera2-1.3.1\AndroidManifest.xml:30:13-32:89
67                android:name="androidx.camera.core.impl.MetadataHolderService.DEFAULT_CONFIG_PROVIDER"
67-->[androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\39959a6f52757fe9191c79dc0e003492\transformed\camera-camera2-1.3.1\AndroidManifest.xml:31:17-103
68                android:value="androidx.camera.camera2.Camera2Config$DefaultProvider" />
68-->[androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\39959a6f52757fe9191c79dc0e003492\transformed\camera-camera2-1.3.1\AndroidManifest.xml:32:17-86
69        </service>
70        <service
70-->[com.google.android.gms:play-services-mlkit-face-detection:17.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aef56dd88d86ed3ca6ef1bad58016681\transformed\play-services-mlkit-face-detection-17.1.0\AndroidManifest.xml:9:9-15:19
71            android:name="com.google.mlkit.common.internal.MlKitComponentDiscoveryService"
71-->[com.google.android.gms:play-services-mlkit-face-detection:17.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aef56dd88d86ed3ca6ef1bad58016681\transformed\play-services-mlkit-face-detection-17.1.0\AndroidManifest.xml:10:13-91
72            android:directBootAware="true"
72-->[com.google.mlkit:common:18.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0b0419a5612395d350313afcebc2aeaf\transformed\common-18.10.0\AndroidManifest.xml:17:13-43
73            android:exported="false" >
73-->[com.google.android.gms:play-services-mlkit-face-detection:17.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aef56dd88d86ed3ca6ef1bad58016681\transformed\play-services-mlkit-face-detection-17.1.0\AndroidManifest.xml:11:13-37
74            <meta-data
74-->[com.google.android.gms:play-services-mlkit-face-detection:17.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aef56dd88d86ed3ca6ef1bad58016681\transformed\play-services-mlkit-face-detection-17.1.0\AndroidManifest.xml:12:13-14:85
75                android:name="com.google.firebase.components:com.google.mlkit.vision.face.internal.FaceRegistrar"
75-->[com.google.android.gms:play-services-mlkit-face-detection:17.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aef56dd88d86ed3ca6ef1bad58016681\transformed\play-services-mlkit-face-detection-17.1.0\AndroidManifest.xml:13:17-114
76                android:value="com.google.firebase.components.ComponentRegistrar" />
76-->[com.google.android.gms:play-services-mlkit-face-detection:17.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aef56dd88d86ed3ca6ef1bad58016681\transformed\play-services-mlkit-face-detection-17.1.0\AndroidManifest.xml:14:17-82
77            <meta-data
77-->[com.google.mlkit:vision-common:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f45b269b8d1a1996fea8c340ec467abf\transformed\vision-common-17.2.0\AndroidManifest.xml:12:13-14:85
78                android:name="com.google.firebase.components:com.google.mlkit.vision.common.internal.VisionCommonRegistrar"
78-->[com.google.mlkit:vision-common:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f45b269b8d1a1996fea8c340ec467abf\transformed\vision-common-17.2.0\AndroidManifest.xml:13:17-124
79                android:value="com.google.firebase.components.ComponentRegistrar" />
79-->[com.google.mlkit:vision-common:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f45b269b8d1a1996fea8c340ec467abf\transformed\vision-common-17.2.0\AndroidManifest.xml:14:17-82
80            <meta-data
80-->[com.google.mlkit:common:18.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0b0419a5612395d350313afcebc2aeaf\transformed\common-18.10.0\AndroidManifest.xml:20:13-22:85
81                android:name="com.google.firebase.components:com.google.mlkit.common.internal.CommonComponentRegistrar"
81-->[com.google.mlkit:common:18.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0b0419a5612395d350313afcebc2aeaf\transformed\common-18.10.0\AndroidManifest.xml:21:17-120
82                android:value="com.google.firebase.components.ComponentRegistrar" />
82-->[com.google.mlkit:common:18.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0b0419a5612395d350313afcebc2aeaf\transformed\common-18.10.0\AndroidManifest.xml:22:17-82
83        </service>
84
85        <provider
85-->[com.google.mlkit:common:18.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0b0419a5612395d350313afcebc2aeaf\transformed\common-18.10.0\AndroidManifest.xml:9:9-13:38
86            android:name="com.google.mlkit.common.internal.MlKitInitProvider"
86-->[com.google.mlkit:common:18.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0b0419a5612395d350313afcebc2aeaf\transformed\common-18.10.0\AndroidManifest.xml:10:13-78
87            android:authorities="com.wendy.face.mlkitinitprovider"
87-->[com.google.mlkit:common:18.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0b0419a5612395d350313afcebc2aeaf\transformed\common-18.10.0\AndroidManifest.xml:11:13-69
88            android:exported="false"
88-->[com.google.mlkit:common:18.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0b0419a5612395d350313afcebc2aeaf\transformed\common-18.10.0\AndroidManifest.xml:12:13-37
89            android:initOrder="99" />
89-->[com.google.mlkit:common:18.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0b0419a5612395d350313afcebc2aeaf\transformed\common-18.10.0\AndroidManifest.xml:13:13-35
90
91        <activity
91-->[androidx.compose.ui:ui-tooling-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c9736fdf0fb2243f2547120df2dfc721\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
92            android:name="androidx.compose.ui.tooling.PreviewActivity"
92-->[androidx.compose.ui:ui-tooling-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c9736fdf0fb2243f2547120df2dfc721\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
93            android:exported="true" />
93-->[androidx.compose.ui:ui-tooling-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c9736fdf0fb2243f2547120df2dfc721\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
94        <activity
94-->[androidx.compose.ui:ui-test-manifest:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\29891f8b28b8cddc69a28c96172dde33\transformed\ui-test-manifest-1.7.0\AndroidManifest.xml:23:9-25:39
95            android:name="androidx.activity.ComponentActivity"
95-->[androidx.compose.ui:ui-test-manifest:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\29891f8b28b8cddc69a28c96172dde33\transformed\ui-test-manifest-1.7.0\AndroidManifest.xml:24:13-63
96            android:exported="true" />
96-->[androidx.compose.ui:ui-test-manifest:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\29891f8b28b8cddc69a28c96172dde33\transformed\ui-test-manifest-1.7.0\AndroidManifest.xml:25:13-36
97        <activity
97-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b463a313bba75631a42e21f95a59b107\transformed\play-services-base-18.1.0\AndroidManifest.xml:20:9-22:45
98            android:name="com.google.android.gms.common.api.GoogleApiActivity"
98-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b463a313bba75631a42e21f95a59b107\transformed\play-services-base-18.1.0\AndroidManifest.xml:20:19-85
99            android:exported="false"
99-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b463a313bba75631a42e21f95a59b107\transformed\play-services-base-18.1.0\AndroidManifest.xml:22:19-43
100            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
100-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b463a313bba75631a42e21f95a59b107\transformed\play-services-base-18.1.0\AndroidManifest.xml:21:19-78
101
102        <meta-data
102-->[com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bd665940b510ccfb87ca6845c9679e75\transformed\play-services-basement-18.1.0\AndroidManifest.xml:21:9-23:69
103            android:name="com.google.android.gms.version"
103-->[com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bd665940b510ccfb87ca6845c9679e75\transformed\play-services-basement-18.1.0\AndroidManifest.xml:22:13-58
104            android:value="@integer/google_play_services_version" />
104-->[com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bd665940b510ccfb87ca6845c9679e75\transformed\play-services-basement-18.1.0\AndroidManifest.xml:23:13-66
105
106        <provider
106-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9caa547508a012762f421c3c63e36d4\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
107            android:name="androidx.startup.InitializationProvider"
107-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9caa547508a012762f421c3c63e36d4\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
108            android:authorities="com.wendy.face.androidx-startup"
108-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9caa547508a012762f421c3c63e36d4\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
109            android:exported="false" >
109-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9caa547508a012762f421c3c63e36d4\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
110            <meta-data
110-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9caa547508a012762f421c3c63e36d4\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
111                android:name="androidx.emoji2.text.EmojiCompatInitializer"
111-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9caa547508a012762f421c3c63e36d4\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
112                android:value="androidx.startup" />
112-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9caa547508a012762f421c3c63e36d4\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
113            <meta-data
113-->[androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\59ce49f538141d4c650699bebc97fc75\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:29:13-31:52
114                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
114-->[androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\59ce49f538141d4c650699bebc97fc75\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:30:17-78
115                android:value="androidx.startup" />
115-->[androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\59ce49f538141d4c650699bebc97fc75\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:31:17-49
116            <meta-data
116-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ad40a95904d22e4f36c088f0516ab14e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
117                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
117-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ad40a95904d22e4f36c088f0516ab14e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
118                android:value="androidx.startup" />
118-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ad40a95904d22e4f36c088f0516ab14e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
119        </provider>
120
121        <service
121-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\62f67fda4d973d784ed6d16c274509a5\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:29:9-35:19
122            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
122-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\62f67fda4d973d784ed6d16c274509a5\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:30:13-103
123            android:exported="false" >
123-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\62f67fda4d973d784ed6d16c274509a5\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:31:13-37
124            <meta-data
124-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\62f67fda4d973d784ed6d16c274509a5\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:32:13-34:39
125                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
125-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\62f67fda4d973d784ed6d16c274509a5\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:33:17-94
126                android:value="cct" />
126-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\62f67fda4d973d784ed6d16c274509a5\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:34:17-36
127        </service>
128        <service
128-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\53dbc1e4706c8938f397d542498cd2dc\transformed\transport-runtime-2.2.6\AndroidManifest.xml:26:9-30:19
129            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
129-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\53dbc1e4706c8938f397d542498cd2dc\transformed\transport-runtime-2.2.6\AndroidManifest.xml:27:13-117
130            android:exported="false"
130-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\53dbc1e4706c8938f397d542498cd2dc\transformed\transport-runtime-2.2.6\AndroidManifest.xml:28:13-37
131            android:permission="android.permission.BIND_JOB_SERVICE" >
131-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\53dbc1e4706c8938f397d542498cd2dc\transformed\transport-runtime-2.2.6\AndroidManifest.xml:29:13-69
132        </service>
133
134        <receiver
134-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\53dbc1e4706c8938f397d542498cd2dc\transformed\transport-runtime-2.2.6\AndroidManifest.xml:32:9-34:40
135            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
135-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\53dbc1e4706c8938f397d542498cd2dc\transformed\transport-runtime-2.2.6\AndroidManifest.xml:33:13-132
136            android:exported="false" />
136-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\53dbc1e4706c8938f397d542498cd2dc\transformed\transport-runtime-2.2.6\AndroidManifest.xml:34:13-37
137        <receiver
137-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ad40a95904d22e4f36c088f0516ab14e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
138            android:name="androidx.profileinstaller.ProfileInstallReceiver"
138-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ad40a95904d22e4f36c088f0516ab14e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
139            android:directBootAware="false"
139-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ad40a95904d22e4f36c088f0516ab14e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
140            android:enabled="true"
140-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ad40a95904d22e4f36c088f0516ab14e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
141            android:exported="true"
141-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ad40a95904d22e4f36c088f0516ab14e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
142            android:permission="android.permission.DUMP" >
142-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ad40a95904d22e4f36c088f0516ab14e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
143            <intent-filter>
143-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ad40a95904d22e4f36c088f0516ab14e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
144                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
144-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ad40a95904d22e4f36c088f0516ab14e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
144-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ad40a95904d22e4f36c088f0516ab14e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
145            </intent-filter>
146            <intent-filter>
146-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ad40a95904d22e4f36c088f0516ab14e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
147                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
147-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ad40a95904d22e4f36c088f0516ab14e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
147-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ad40a95904d22e4f36c088f0516ab14e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
148            </intent-filter>
149            <intent-filter>
149-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ad40a95904d22e4f36c088f0516ab14e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
150                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
150-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ad40a95904d22e4f36c088f0516ab14e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
150-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ad40a95904d22e4f36c088f0516ab14e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
151            </intent-filter>
152            <intent-filter>
152-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ad40a95904d22e4f36c088f0516ab14e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
153                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
153-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ad40a95904d22e4f36c088f0516ab14e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
153-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ad40a95904d22e4f36c088f0516ab14e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
154            </intent-filter>
155        </receiver>
156    </application>
157
158</manifest>
