package com.wendy.face

import android.Manifest
import android.content.pm.PackageManager
import android.os.Bundle
import android.util.Log
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.result.contract.ActivityResultContracts
import androidx.camera.core.CameraSelector
import androidx.camera.core.ImageAnalysis
import androidx.camera.core.ImageCapture
import androidx.camera.core.ImageCaptureException
import androidx.camera.core.Preview
import androidx.camera.lifecycle.ProcessCameraProvider
import androidx.camera.view.PreviewView
import androidx.compose.foundation.Canvas
import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material3.Button
import androidx.compose.material3.FloatingActionButton
import androidx.compose.material3.Text
import androidx.compose.ui.graphics.asImageBitmap
import androidx.compose.ui.layout.ContentScale
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.net.Uri
import androidx.compose.ui.unit.dp
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.graphics.Path
import androidx.compose.ui.graphics.drawscope.DrawScope
import com.google.mlkit.vision.face.FaceContour
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalLifecycleOwner
import androidx.compose.ui.viewinterop.AndroidView
import androidx.core.content.ContextCompat
import com.google.mlkit.vision.common.InputImage
import com.google.mlkit.vision.face.Face
import com.google.mlkit.vision.face.FaceDetection
import com.google.mlkit.vision.face.FaceDetectorOptions
import com.wendy.face.ui.theme.FaceTheme
import java.io.File
import java.text.SimpleDateFormat
import java.util.Locale
import java.util.concurrent.Executors

class MainActivity : ComponentActivity() {

    private val requestMultiplePermissionsLauncher =
        registerForActivityResult(ActivityResultContracts.RequestMultiplePermissions()) { permissions ->
            permissions.entries.forEach { entry ->
                val permission = entry.key
                val isGranted = entry.value
                if (isGranted) {
                    Log.d("MainActivity", "Permission granted: $permission")
                } else {
                    Log.w("MainActivity", "Permission denied: $permission")
                }
            }
        }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        // Check and request permissions
        val permissionsToRequest = mutableListOf<String>()

        if (ContextCompat.checkSelfPermission(this, Manifest.permission.CAMERA) != PackageManager.PERMISSION_GRANTED) {
            permissionsToRequest.add(Manifest.permission.CAMERA)
        }

        if (android.os.Build.VERSION.SDK_INT <= android.os.Build.VERSION_CODES.P &&
            ContextCompat.checkSelfPermission(this, Manifest.permission.WRITE_EXTERNAL_STORAGE) != PackageManager.PERMISSION_GRANTED) {
            permissionsToRequest.add(Manifest.permission.WRITE_EXTERNAL_STORAGE)
        }

        if (permissionsToRequest.isNotEmpty()) {
            requestMultiplePermissionsLauncher.launch(permissionsToRequest.toTypedArray())
        }

        setContent {
            FaceTheme {
                var faces by remember { mutableStateOf<List<Face>>(emptyList()) }
                var imageWidth by remember { mutableStateOf(0) }
                var imageHeight by remember { mutableStateOf(0) }
                var isBackCamera by remember { mutableStateOf(false) }
                var capturedImageUri by remember { mutableStateOf<Uri?>(null) }
                var capturedBitmap by remember { mutableStateOf<Bitmap?>(null) }
                var showCamera by remember { mutableStateOf(true) }

                Box(modifier = Modifier.fillMaxSize()) {
                    if (showCamera) {
                        CameraView(
                            isBackCamera = isBackCamera,
                            onFacesDetected = { detectedFaces, width, height ->
                                faces = detectedFaces
                                imageWidth = width
                                imageHeight = height
                            },
                            onCameraSwitch = { isBackCamera = !isBackCamera },
                            onImageCaptured = { uri, bitmap ->
                                capturedImageUri = uri
                                capturedBitmap = bitmap
                                showCamera = false
                            }
                        )
                    } else {
                        // 显示拍摄的照片
                        capturedBitmap?.let { bitmap ->
                            Image(
                                bitmap = bitmap.asImageBitmap(),
                                contentDescription = "Captured Photo",
                                modifier = Modifier.fillMaxSize(),
                                contentScale = ContentScale.Fit
                            )
                        }

                        // 控制按钮
                        Row(
                            modifier = Modifier
                                .align(androidx.compose.ui.Alignment.TopEnd)
                                .padding(16.dp),
                            horizontalArrangement = Arrangement.spacedBy(8.dp)
                        ) {
                            Button(
                                onClick = {
                                    showCamera = true
                                    faces = emptyList()
                                    capturedImageUri = null
                                    capturedBitmap = null
                                }
                            ) {
                                Text("返回摄像头")
                            }

                            Button(
                                onClick = {
                                    // 使用测试图片进行人脸检测
                                    testFaceDetection(this@MainActivity) { testFaces, testWidth, testHeight, testBitmap ->
                                        faces = testFaces
                                        imageWidth = testWidth
                                        imageHeight = testHeight
                                        capturedBitmap = testBitmap
                                    }
                                }
                            ) {
                                Text("测试检测")
                            }
                        }
                    }

                    FaceOverlay(faces = faces, imageWidth = imageWidth, imageHeight = imageHeight)
                }
            }
        }
    }
}

@Composable
fun CameraView(
    isBackCamera: Boolean,
    onFacesDetected: (List<Face>, Int, Int) -> Unit,
    onCameraSwitch: () -> Unit,
    onImageCaptured: (Uri?, Bitmap?) -> Unit
) {
    val context = LocalContext.current
    val lifecycleOwner = LocalLifecycleOwner.current
    val cameraProviderFuture = remember { ProcessCameraProvider.getInstance(context) }
    val cameraExecutor = remember { Executors.newSingleThreadExecutor() }
    var imageCapture by remember { mutableStateOf<ImageCapture?>(null) }
    var cameraProvider by remember { mutableStateOf<ProcessCameraProvider?>(null) }
    var previewView by remember { mutableStateOf<PreviewView?>(null) }

    val faceDetectorOptions = FaceDetectorOptions.Builder()
        .setPerformanceMode(FaceDetectorOptions.PERFORMANCE_MODE_ACCURATE)
        .setLandmarkMode(FaceDetectorOptions.LANDMARK_MODE_ALL)
        .setClassificationMode(FaceDetectorOptions.CLASSIFICATION_MODE_ALL)
        .setContourMode(FaceDetectorOptions.CONTOUR_MODE_ALL)
        .setMinFaceSize(0.05f)  // 降低最小人脸大小阈值
        .enableTracking()
        .build()
    val faceDetector = FaceDetection.getClient(faceDetectorOptions)

    // 绑定摄像头的函数
    fun bindCamera(preview: PreviewView) {
        Log.d("CameraView", "bindCamera called, isBackCamera: $isBackCamera")
        cameraProviderFuture.addListener({
            try {
                cameraProvider = cameraProviderFuture.get()
                Log.d("CameraView", "CameraProvider obtained")

                val previewUseCase = Preview.Builder().build().also {
                    it.setSurfaceProvider(preview.surfaceProvider)
                }

                imageCapture = ImageCapture.Builder()
                    .setCaptureMode(ImageCapture.CAPTURE_MODE_MAXIMIZE_QUALITY)
                    .build()

                val cameraSelector = if (isBackCamera) {
                    CameraSelector.DEFAULT_BACK_CAMERA
                } else {
                    CameraSelector.DEFAULT_FRONT_CAMERA
                }

                cameraProvider?.unbindAll()
                cameraProvider?.bindToLifecycle(
                    lifecycleOwner,
                    cameraSelector,
                    previewUseCase,
                    imageCapture
                )
                Log.d("CameraView", "Camera bound successfully")
            } catch (exc: Exception) {
                Log.e("CameraView", "Use case binding failed", exc)
            }
        }, androidx.core.content.ContextCompat.getMainExecutor(context))
    }

    Box(modifier = Modifier.fillMaxSize()) {
        AndroidView(
            modifier = Modifier.fillMaxSize(),
            factory = { ctx ->
                val preview = PreviewView(ctx)
                previewView = preview
                Log.d("CameraView", "PreviewView created")
                bindCamera(preview)
                preview
            },
            update = { preview ->
                Log.d("CameraView", "AndroidView update called")
                bindCamera(preview)
            }
        )

        // Camera controls
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
                .align(androidx.compose.ui.Alignment.BottomCenter),
            horizontalArrangement = Arrangement.SpaceEvenly
        ) {
            // Camera switch button
            Button(
                onClick = {
                    Log.d("CameraView", "Camera switch button clicked")
                    onCameraSwitch()
                }
            ) {
                Text("切换摄像头")
            }

            // Capture button
            Button(
                onClick = {
                    Log.d("CameraView", "Capture button clicked, imageCapture: $imageCapture")
                    imageCapture?.let { capture ->
                        takePicture(capture, context, faceDetector, onFacesDetected, onImageCaptured)
                    } ?: Log.w("CameraView", "ImageCapture is null")
                }
            ) {
                Text("拍照检测")
            }
        }
    }
}

private fun takePicture(
    imageCapture: ImageCapture,
    context: android.content.Context,
    faceDetector: com.google.mlkit.vision.face.FaceDetector,
    onFacesDetected: (List<Face>, Int, Int) -> Unit,
    onImageCaptured: (Uri?, Bitmap?) -> Unit
) {
    val name = SimpleDateFormat("yyyy-MM-dd-HH-mm-ss-SSS", Locale.US)
        .format(System.currentTimeMillis())
    val contentValues = android.content.ContentValues().apply {
        put(android.provider.MediaStore.MediaColumns.DISPLAY_NAME, name)
        put(android.provider.MediaStore.MediaColumns.MIME_TYPE, "image/jpeg")
        if (android.os.Build.VERSION.SDK_INT > android.os.Build.VERSION_CODES.P) {
            put(android.provider.MediaStore.Images.Media.RELATIVE_PATH, "Pictures/FaceDetection")
        }
    }

    val outputOptions = ImageCapture.OutputFileOptions.Builder(
        context.contentResolver,
        android.provider.MediaStore.Images.Media.EXTERNAL_CONTENT_URI,
        contentValues
    ).build()

    imageCapture.takePicture(
        outputOptions,
        androidx.core.content.ContextCompat.getMainExecutor(context),
        object : ImageCapture.OnImageSavedCallback {
            override fun onError(exception: ImageCaptureException) {
                Log.e("CameraView", "Photo capture failed: ${exception.message}", exception)
            }

            override fun onImageSaved(output: ImageCapture.OutputFileResults) {
                Log.d("CameraView", "Photo capture succeeded: ${output.savedUri}")

                // Load the captured image as bitmap and process for face detection
                output.savedUri?.let { uri ->
                    try {
                        // Load bitmap from URI
                        val inputStream = context.contentResolver.openInputStream(uri)
                        val bitmap = BitmapFactory.decodeStream(inputStream)
                        inputStream?.close()

                        if (bitmap != null) {
                            // Create InputImage from bitmap for face detection
                            val inputImage = InputImage.fromBitmap(bitmap, 0)

                            // Perform face detection
                            faceDetector.process(inputImage)
                                .addOnSuccessListener { detectedFaces ->
                                    Log.d("CameraView", "Face detection completed, faces found: ${detectedFaces.size}")
                                    Log.d("CameraView", "Image dimensions: ${bitmap.width} x ${bitmap.height}")
                                    detectedFaces.forEachIndexed { index, face ->
                                        Log.d("CameraView", "Face $index: boundingBox=${face.boundingBox}, landmarks=${face.allLandmarks.size}")
                                    }
                                    onFacesDetected(detectedFaces, bitmap.width, bitmap.height)
                                }
                                .addOnFailureListener { e ->
                                    Log.e("CameraView", "Face detection on captured image failed", e)
                                }

                            // Call the callback with the captured image
                            onImageCaptured(uri, bitmap)
                        } else {
                            Log.e("CameraView", "Failed to decode bitmap from URI")
                        }
                    } catch (e: Exception) {
                        Log.e("CameraView", "Error processing captured image", e)
                    }
                }
            }
        }
    )
}

private fun testFaceDetection(
    context: android.content.Context,
    onFacesDetected: (List<Face>, Int, Int, Bitmap) -> Unit
) {
    // 创建一个简单的测试图片（纯色图片用于测试坐标系统）
    val testBitmap = Bitmap.createBitmap(400, 300, Bitmap.Config.ARGB_8888)
    testBitmap.eraseColor(android.graphics.Color.BLUE)

    // 在图片上绘制一些测试图形
    val canvas = android.graphics.Canvas(testBitmap)
    val paint = android.graphics.Paint().apply {
        color = android.graphics.Color.RED
        strokeWidth = 5f
        style = android.graphics.Paint.Style.STROKE
    }

    // 绘制一个矩形作为"假人脸"用于测试坐标
    canvas.drawRect(100f, 75f, 300f, 225f, paint)

    // 绘制一些圆点作为"假关键点"
    val pointPaint = android.graphics.Paint().apply {
        color = android.graphics.Color.YELLOW
        style = android.graphics.Paint.Style.FILL
    }
    canvas.drawCircle(150f, 125f, 8f, pointPaint) // 左眼
    canvas.drawCircle(250f, 125f, 8f, pointPaint) // 右眼
    canvas.drawCircle(200f, 150f, 8f, pointPaint) // 鼻子
    canvas.drawCircle(200f, 175f, 8f, pointPaint) // 嘴巴

    Log.d("TestFaceDetection", "Created test bitmap: ${testBitmap.width} x ${testBitmap.height}")

    // 创建一个假的人脸检测结果用于测试坐标转换
    val testFaces = emptyList<Face>() // 暂时使用空列表，主要测试图片显示和坐标系统
    onFacesDetected(testFaces, testBitmap.width, testBitmap.height, testBitmap)
}

@Composable
fun FaceOverlay(faces: List<Face>, imageWidth: Int, imageHeight: Int) {
    Canvas(modifier = Modifier.fillMaxSize()) {
        if (imageWidth == 0 || imageHeight == 0 || faces.isEmpty()) return@Canvas

        val viewWidth = size.width
        val viewHeight = size.height

        // 计算图片在视图中的实际显示区域（考虑ContentScale.Fit）
        val imageAspectRatio = imageWidth.toFloat() / imageHeight.toFloat()
        val viewAspectRatio = viewWidth / viewHeight

        val (displayWidth, displayHeight, offsetX, offsetY) = if (imageAspectRatio > viewAspectRatio) {
            // 图片更宽，以宽度为准
            val displayWidth = viewWidth
            val displayHeight = viewWidth / imageAspectRatio
            val offsetY = (viewHeight - displayHeight) / 2f
            arrayOf(displayWidth, displayHeight, 0f, offsetY)
        } else {
            // 图片更高，以高度为准
            val displayHeight = viewHeight
            val displayWidth = viewHeight * imageAspectRatio
            val offsetX = (viewWidth - displayWidth) / 2f
            arrayOf(displayWidth, displayHeight, offsetX, 0f)
        }

        val scaleX = displayWidth / imageWidth.toFloat()
        val scaleY = displayHeight / imageHeight.toFloat()

        Log.d("FaceOverlay", "View: ${viewWidth}x${viewHeight}, Image: ${imageWidth}x${imageHeight}")
        Log.d("FaceOverlay", "Display: ${displayWidth}x${displayHeight}, Offset: (${offsetX}, ${offsetY})")
        Log.d("FaceOverlay", "Scale: (${scaleX}, ${scaleY})")

        for (face in faces) {
            // Draw bounding box
            val boundingBox = face.boundingBox
            val left = offsetX + boundingBox.left * scaleX
            val top = offsetY + boundingBox.top * scaleY
            val right = offsetX + boundingBox.right * scaleX
            val bottom = offsetY + boundingBox.bottom * scaleY

            drawRect(
                color = Color.Red,
                topLeft = Offset(left, top),
                size = androidx.compose.ui.geometry.Size(right - left, bottom - top),
                style = Stroke(width = 3f)
            )

            // Draw face contours
            drawFaceContours(face, displayWidth, displayHeight, offsetX, offsetY, scaleX, scaleY)

            // Draw landmarks with different colors for different types
            face.allLandmarks.forEach { landmark ->
                val landmarkPosition = landmark.position
                val x = offsetX + landmarkPosition.x * scaleX
                val y = offsetY + landmarkPosition.y * scaleY

                val color = when (landmark.landmarkType) {
                    com.google.mlkit.vision.face.FaceLandmark.MOUTH_BOTTOM,
                    com.google.mlkit.vision.face.FaceLandmark.MOUTH_LEFT,
                    com.google.mlkit.vision.face.FaceLandmark.MOUTH_RIGHT -> Color.Magenta
                    com.google.mlkit.vision.face.FaceLandmark.LEFT_EYE,
                    com.google.mlkit.vision.face.FaceLandmark.RIGHT_EYE -> Color.Cyan
                    com.google.mlkit.vision.face.FaceLandmark.NOSE_BASE -> Color.Green
                    com.google.mlkit.vision.face.FaceLandmark.LEFT_EAR,
                    com.google.mlkit.vision.face.FaceLandmark.RIGHT_EAR -> Color.Blue
                    else -> Color.Yellow
                }

                drawCircle(
                    color = color,
                    center = Offset(x, y),
                    radius = 5f
                )
            }
        }
    }
}

fun DrawScope.drawFaceContours(face: Face, displayWidth: Float, displayHeight: Float, offsetX: Float, offsetY: Float, scaleX: Float, scaleY: Float) {
    val contourTypes = listOf(
        FaceContour.FACE,
        FaceContour.LEFT_EYEBROW_TOP,
        FaceContour.LEFT_EYEBROW_BOTTOM,
        FaceContour.RIGHT_EYEBROW_TOP,
        FaceContour.RIGHT_EYEBROW_BOTTOM,
        FaceContour.LEFT_EYE,
        FaceContour.RIGHT_EYE,
        FaceContour.UPPER_LIP_TOP,
        FaceContour.UPPER_LIP_BOTTOM,
        FaceContour.LOWER_LIP_TOP,
        FaceContour.LOWER_LIP_BOTTOM,
        FaceContour.NOSE_BRIDGE,
        FaceContour.NOSE_BOTTOM
    )

    contourTypes.forEach { contourType ->
        val contour = face.getContour(contourType)
        contour?.let { faceContour ->
            val points = faceContour.points
            if (points.size > 1) {
                val path = Path()
                val firstPoint = points[0]
                val startX = offsetX + firstPoint.x * scaleX
                val startY = offsetY + firstPoint.y * scaleY
                path.moveTo(startX, startY)

                for (i in 1 until points.size) {
                    val point = points[i]
                    val x = offsetX + point.x * scaleX
                    val y = offsetY + point.y * scaleY
                    path.lineTo(x, y)
                }

                // Close path for certain contours
                if (contourType == FaceContour.FACE ||
                    contourType == FaceContour.LEFT_EYE ||
                    contourType == FaceContour.RIGHT_EYE) {
                    path.close()
                }

                val color = when (contourType) {
                    FaceContour.FACE -> Color.Red
                    FaceContour.LEFT_EYE, FaceContour.RIGHT_EYE -> Color.Cyan
                    FaceContour.LEFT_EYEBROW_TOP, FaceContour.LEFT_EYEBROW_BOTTOM,
                    FaceContour.RIGHT_EYEBROW_TOP, FaceContour.RIGHT_EYEBROW_BOTTOM -> Color.Blue
                    FaceContour.UPPER_LIP_TOP, FaceContour.UPPER_LIP_BOTTOM,
                    FaceContour.LOWER_LIP_TOP, FaceContour.LOWER_LIP_BOTTOM -> Color.Magenta
                    FaceContour.NOSE_BRIDGE, FaceContour.NOSE_BOTTOM -> Color.Green
                    else -> Color.White
                }

                drawPath(
                    path = path,
                    color = color,
                    style = Stroke(width = 2f)
                )
            }
        }
    }
}