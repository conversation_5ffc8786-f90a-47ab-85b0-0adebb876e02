package com.wendy.face

import android.Manifest
import android.content.pm.PackageManager
import android.os.Bundle
import android.util.Log
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.result.contract.ActivityResultContracts
import androidx.camera.core.CameraSelector
import androidx.camera.core.ImageAnalysis
import androidx.camera.core.ImageCapture
import androidx.camera.core.ImageCaptureException
import androidx.camera.core.Preview
import androidx.camera.lifecycle.ProcessCameraProvider
import androidx.camera.view.PreviewView
import androidx.compose.foundation.Canvas
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material3.Button
import androidx.compose.material3.FloatingActionButton
import androidx.compose.material3.Text
import androidx.compose.ui.unit.dp
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.graphics.Path
import androidx.compose.ui.graphics.drawscope.DrawScope
import com.google.mlkit.vision.face.FaceContour
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalLifecycleOwner
import androidx.compose.ui.viewinterop.AndroidView
import androidx.core.content.ContextCompat
import com.google.mlkit.vision.common.InputImage
import com.google.mlkit.vision.face.Face
import com.google.mlkit.vision.face.FaceDetection
import com.google.mlkit.vision.face.FaceDetectorOptions
import com.wendy.face.ui.theme.FaceTheme
import java.io.File
import java.text.SimpleDateFormat
import java.util.Locale
import java.util.concurrent.Executors

class MainActivity : ComponentActivity() {

    private val requestMultiplePermissionsLauncher =
        registerForActivityResult(ActivityResultContracts.RequestMultiplePermissions()) { permissions ->
            permissions.entries.forEach { entry ->
                val permission = entry.key
                val isGranted = entry.value
                if (isGranted) {
                    Log.d("MainActivity", "Permission granted: $permission")
                } else {
                    Log.w("MainActivity", "Permission denied: $permission")
                }
            }
        }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        // Check and request permissions
        val permissionsToRequest = mutableListOf<String>()

        if (ContextCompat.checkSelfPermission(this, Manifest.permission.CAMERA) != PackageManager.PERMISSION_GRANTED) {
            permissionsToRequest.add(Manifest.permission.CAMERA)
        }

        if (android.os.Build.VERSION.SDK_INT <= android.os.Build.VERSION_CODES.P &&
            ContextCompat.checkSelfPermission(this, Manifest.permission.WRITE_EXTERNAL_STORAGE) != PackageManager.PERMISSION_GRANTED) {
            permissionsToRequest.add(Manifest.permission.WRITE_EXTERNAL_STORAGE)
        }

        if (permissionsToRequest.isNotEmpty()) {
            requestMultiplePermissionsLauncher.launch(permissionsToRequest.toTypedArray())
        }

        setContent {
            FaceTheme {
                var faces by remember { mutableStateOf<List<Face>>(emptyList()) }
                var imageWidth by remember { mutableStateOf(0) }
                var imageHeight by remember { mutableStateOf(0) }
                var isBackCamera by remember { mutableStateOf(false) }

                Box(modifier = Modifier.fillMaxSize()) {
                    CameraView(
                        isBackCamera = isBackCamera,
                        onFacesDetected = { detectedFaces, width, height ->
                            faces = detectedFaces
                            imageWidth = width
                            imageHeight = height
                        },
                        onCameraSwitch = { isBackCamera = !isBackCamera }
                    )
                    FaceOverlay(faces = faces, imageWidth = imageWidth, imageHeight = imageHeight)
                }
            }
        }
    }
}

@Composable
fun CameraView(
    isBackCamera: Boolean,
    onFacesDetected: (List<Face>, Int, Int) -> Unit,
    onCameraSwitch: () -> Unit
) {
    val context = LocalContext.current
    val lifecycleOwner = LocalLifecycleOwner.current
    val cameraProviderFuture = remember { ProcessCameraProvider.getInstance(context) }
    val cameraExecutor = remember { Executors.newSingleThreadExecutor() }
    var imageCapture by remember { mutableStateOf<ImageCapture?>(null) }
    var cameraProvider by remember { mutableStateOf<ProcessCameraProvider?>(null) }
    var previewView by remember { mutableStateOf<PreviewView?>(null) }

    val faceDetectorOptions = FaceDetectorOptions.Builder()
        .setPerformanceMode(FaceDetectorOptions.PERFORMANCE_MODE_ACCURATE)
        .setLandmarkMode(FaceDetectorOptions.LANDMARK_MODE_ALL)
        .setClassificationMode(FaceDetectorOptions.CLASSIFICATION_MODE_ALL)
        .setContourMode(FaceDetectorOptions.CONTOUR_MODE_ALL)
        .setMinFaceSize(0.1f)
        .enableTracking()
        .build()
    val faceDetector = FaceDetection.getClient(faceDetectorOptions)

    // 绑定摄像头的函数
    fun bindCamera(preview: PreviewView) {
        Log.d("CameraView", "bindCamera called, isBackCamera: $isBackCamera")
        cameraProviderFuture.addListener({
            try {
                cameraProvider = cameraProviderFuture.get()
                Log.d("CameraView", "CameraProvider obtained")

                val previewUseCase = Preview.Builder().build().also {
                    it.setSurfaceProvider(preview.surfaceProvider)
                }

                imageCapture = ImageCapture.Builder()
                    .setCaptureMode(ImageCapture.CAPTURE_MODE_MAXIMIZE_QUALITY)
                    .build()

                val cameraSelector = if (isBackCamera) {
                    CameraSelector.DEFAULT_BACK_CAMERA
                } else {
                    CameraSelector.DEFAULT_FRONT_CAMERA
                }

                cameraProvider?.unbindAll()
                cameraProvider?.bindToLifecycle(
                    lifecycleOwner,
                    cameraSelector,
                    previewUseCase,
                    imageCapture
                )
                Log.d("CameraView", "Camera bound successfully")
            } catch (exc: Exception) {
                Log.e("CameraView", "Use case binding failed", exc)
            }
        }, androidx.core.content.ContextCompat.getMainExecutor(context))
    }

    Box(modifier = Modifier.fillMaxSize()) {
        AndroidView(
            modifier = Modifier.fillMaxSize(),
            factory = { ctx ->
                val preview = PreviewView(ctx)
                previewView = preview
                Log.d("CameraView", "PreviewView created")
                preview
            }
        )

        // Camera controls
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
                .align(androidx.compose.ui.Alignment.BottomCenter),
            horizontalArrangement = Arrangement.SpaceEvenly
        ) {
            // Camera switch button
            Button(
                onClick = {
                    Log.d("CameraView", "Camera switch button clicked")
                    onCameraSwitch()
                }
            ) {
                Text("切换摄像头")
            }

            // Capture button
            Button(
                onClick = {
                    Log.d("CameraView", "Capture button clicked, imageCapture: $imageCapture")
                    imageCapture?.let { capture ->
                        takePicture(capture, context, faceDetector, onFacesDetected)
                    } ?: Log.w("CameraView", "ImageCapture is null")
                }
            ) {
                Text("拍照检测")
            }
        }
    }
}

private fun takePicture(
    imageCapture: ImageCapture,
    context: android.content.Context,
    faceDetector: com.google.mlkit.vision.face.FaceDetector,
    onFacesDetected: (List<Face>, Int, Int) -> Unit
) {
    val name = SimpleDateFormat("yyyy-MM-dd-HH-mm-ss-SSS", Locale.US)
        .format(System.currentTimeMillis())
    val contentValues = android.content.ContentValues().apply {
        put(android.provider.MediaStore.MediaColumns.DISPLAY_NAME, name)
        put(android.provider.MediaStore.MediaColumns.MIME_TYPE, "image/jpeg")
        if (android.os.Build.VERSION.SDK_INT > android.os.Build.VERSION_CODES.P) {
            put(android.provider.MediaStore.Images.Media.RELATIVE_PATH, "Pictures/FaceDetection")
        }
    }

    val outputOptions = ImageCapture.OutputFileOptions.Builder(
        context.contentResolver,
        android.provider.MediaStore.Images.Media.EXTERNAL_CONTENT_URI,
        contentValues
    ).build()

    imageCapture.takePicture(
        outputOptions,
        androidx.core.content.ContextCompat.getMainExecutor(context),
        object : ImageCapture.OnImageSavedCallback {
            override fun onError(exception: ImageCaptureException) {
                Log.e("CameraView", "Photo capture failed: ${exception.message}", exception)
            }

            override fun onImageSaved(output: ImageCapture.OutputFileResults) {
                Log.d("CameraView", "Photo capture succeeded: ${output.savedUri}")

                // Process the captured image for face detection
                output.savedUri?.let { uri ->
                    try {
                        val inputImage = InputImage.fromFilePath(context, uri)
                        faceDetector.process(inputImage)
                            .addOnSuccessListener { detectedFaces ->
                                onFacesDetected(detectedFaces, inputImage.width, inputImage.height)
                            }
                            .addOnFailureListener { e ->
                                Log.e("CameraView", "Face detection on captured image failed", e)
                            }
                    } catch (e: Exception) {
                        Log.e("CameraView", "Error processing captured image", e)
                    }
                }
            }
        }
    )
}

@Composable
fun FaceOverlay(faces: List<Face>, imageWidth: Int, imageHeight: Int) {
    Canvas(modifier = Modifier.fillMaxSize()) {
        val viewWidth = size.width
        val viewHeight = size.height
        val scaleX = viewWidth / imageWidth.toFloat()
        val scaleY = viewHeight / imageHeight.toFloat()

        for (face in faces) {
            // Draw bounding box
            val boundingBox = face.boundingBox
            val left = viewWidth - boundingBox.right * scaleX
            val top = boundingBox.top * scaleY
            val right = viewWidth - boundingBox.left * scaleX
            val bottom = boundingBox.bottom * scaleY

            drawRect(
                color = Color.Red,
                topLeft = Offset(left, top),
                size = androidx.compose.ui.geometry.Size(right - left, bottom - top),
                style = Stroke(width = 3f)
            )

            // Draw face contours
            drawFaceContours(face, viewWidth, scaleX, scaleY)

            // Draw landmarks with different colors for different types
            face.allLandmarks.forEach { landmark ->
                val landmarkPosition = landmark.position
                val x = viewWidth - landmarkPosition.x * scaleX
                val y = landmarkPosition.y * scaleY

                val color = when (landmark.landmarkType) {
                    com.google.mlkit.vision.face.FaceLandmark.MOUTH_BOTTOM,
                    com.google.mlkit.vision.face.FaceLandmark.MOUTH_LEFT,
                    com.google.mlkit.vision.face.FaceLandmark.MOUTH_RIGHT -> Color.Magenta
                    com.google.mlkit.vision.face.FaceLandmark.LEFT_EYE,
                    com.google.mlkit.vision.face.FaceLandmark.RIGHT_EYE -> Color.Cyan
                    com.google.mlkit.vision.face.FaceLandmark.NOSE_BASE -> Color.Green
                    com.google.mlkit.vision.face.FaceLandmark.LEFT_EAR,
                    com.google.mlkit.vision.face.FaceLandmark.RIGHT_EAR -> Color.Blue
                    else -> Color.Yellow
                }

                drawCircle(
                    color = color,
                    center = Offset(x, y),
                    radius = 5f
                )
            }
        }
    }
}

fun DrawScope.drawFaceContours(face: Face, viewWidth: Float, scaleX: Float, scaleY: Float) {
    val contourTypes = listOf(
        FaceContour.FACE,
        FaceContour.LEFT_EYEBROW_TOP,
        FaceContour.LEFT_EYEBROW_BOTTOM,
        FaceContour.RIGHT_EYEBROW_TOP,
        FaceContour.RIGHT_EYEBROW_BOTTOM,
        FaceContour.LEFT_EYE,
        FaceContour.RIGHT_EYE,
        FaceContour.UPPER_LIP_TOP,
        FaceContour.UPPER_LIP_BOTTOM,
        FaceContour.LOWER_LIP_TOP,
        FaceContour.LOWER_LIP_BOTTOM,
        FaceContour.NOSE_BRIDGE,
        FaceContour.NOSE_BOTTOM
    )

    contourTypes.forEach { contourType ->
        val contour = face.getContour(contourType)
        contour?.let { faceContour ->
            val points = faceContour.points
            if (points.size > 1) {
                val path = Path()
                val firstPoint = points[0]
                val startX = viewWidth - firstPoint.x * scaleX
                val startY = firstPoint.y * scaleY
                path.moveTo(startX, startY)

                for (i in 1 until points.size) {
                    val point = points[i]
                    val x = viewWidth - point.x * scaleX
                    val y = point.y * scaleY
                    path.lineTo(x, y)
                }

                // Close path for certain contours
                if (contourType == FaceContour.FACE ||
                    contourType == FaceContour.LEFT_EYE ||
                    contourType == FaceContour.RIGHT_EYE) {
                    path.close()
                }

                val color = when (contourType) {
                    FaceContour.FACE -> Color.Red
                    FaceContour.LEFT_EYE, FaceContour.RIGHT_EYE -> Color.Cyan
                    FaceContour.LEFT_EYEBROW_TOP, FaceContour.LEFT_EYEBROW_BOTTOM,
                    FaceContour.RIGHT_EYEBROW_TOP, FaceContour.RIGHT_EYEBROW_BOTTOM -> Color.Blue
                    FaceContour.UPPER_LIP_TOP, FaceContour.UPPER_LIP_BOTTOM,
                    FaceContour.LOWER_LIP_TOP, FaceContour.LOWER_LIP_BOTTOM -> Color.Magenta
                    FaceContour.NOSE_BRIDGE, FaceContour.NOSE_BOTTOM -> Color.Green
                    else -> Color.White
                }

                drawPath(
                    path = path,
                    color = color,
                    style = Stroke(width = 2f)
                )
            }
        }
    }
}