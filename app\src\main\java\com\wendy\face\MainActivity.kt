package com.wendy.face

import android.Manifest
import android.content.pm.PackageManager
import android.os.Bundle
import android.util.Log
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.result.contract.ActivityResultContracts
import androidx.camera.core.CameraSelector
import androidx.camera.core.ImageAnalysis
import androidx.camera.core.ImageCapture
import androidx.camera.core.ImageCaptureException
import androidx.camera.core.Preview
import androidx.camera.lifecycle.ProcessCameraProvider
import androidx.camera.view.PreviewView
import androidx.compose.foundation.Canvas
import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material3.Button
import androidx.compose.material3.FloatingActionButton
import androidx.compose.material3.Text
import androidx.compose.ui.graphics.asImageBitmap
import androidx.compose.ui.layout.ContentScale
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.graphics.Matrix
import android.media.ExifInterface
import android.net.Uri
import androidx.compose.ui.unit.dp
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.graphics.Path
import androidx.compose.ui.graphics.drawscope.DrawScope
import androidx.compose.ui.graphics.PathEffect
import androidx.compose.ui.graphics.StrokeCap
import androidx.compose.ui.graphics.StrokeJoin
import androidx.compose.ui.graphics.Brush
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.background
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.shadow
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import com.google.mlkit.vision.face.FaceContour
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalLifecycleOwner
import androidx.compose.ui.viewinterop.AndroidView
import androidx.core.content.ContextCompat
import com.google.mlkit.vision.common.InputImage
import com.google.mlkit.vision.face.Face
import com.google.mlkit.vision.face.FaceDetection
import com.google.mlkit.vision.face.FaceDetectorOptions
import com.wendy.face.ui.theme.FaceTheme
import java.io.File
import java.text.SimpleDateFormat
import java.util.Locale
import java.util.concurrent.Executors

class MainActivity : ComponentActivity() {

    private val requestMultiplePermissionsLauncher =
        registerForActivityResult(ActivityResultContracts.RequestMultiplePermissions()) { permissions ->
            permissions.entries.forEach { entry ->
                val permission = entry.key
                val isGranted = entry.value
                if (isGranted) {
                    Log.d("MainActivity", "Permission granted: $permission")
                } else {
                    Log.w("MainActivity", "Permission denied: $permission")
                }
            }
        }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        // Check and request permissions
        val permissionsToRequest = mutableListOf<String>()

        if (ContextCompat.checkSelfPermission(this, Manifest.permission.CAMERA) != PackageManager.PERMISSION_GRANTED) {
            permissionsToRequest.add(Manifest.permission.CAMERA)
        }

        if (android.os.Build.VERSION.SDK_INT <= android.os.Build.VERSION_CODES.P &&
            ContextCompat.checkSelfPermission(this, Manifest.permission.WRITE_EXTERNAL_STORAGE) != PackageManager.PERMISSION_GRANTED) {
            permissionsToRequest.add(Manifest.permission.WRITE_EXTERNAL_STORAGE)
        }

        if (permissionsToRequest.isNotEmpty()) {
            requestMultiplePermissionsLauncher.launch(permissionsToRequest.toTypedArray())
        }

        setContent {
            FaceTheme {
                var faces by remember { mutableStateOf<List<Face>>(emptyList()) }
                var imageWidth by remember { mutableStateOf(0) }
                var imageHeight by remember { mutableStateOf(0) }
                var isBackCamera by remember { mutableStateOf(false) }
                var capturedImageUri by remember { mutableStateOf<Uri?>(null) }
                var capturedBitmap by remember { mutableStateOf<Bitmap?>(null) }
                var showCamera by remember { mutableStateOf(true) }

                Box(modifier = Modifier.fillMaxSize()) {
                    if (showCamera) {
                        CameraView(
                            isBackCamera = isBackCamera,
                            onFacesDetected = { detectedFaces, width, height ->
                                faces = detectedFaces
                                imageWidth = width
                                imageHeight = height
                            },
                            onCameraSwitch = { isBackCamera = !isBackCamera },
                            onImageCaptured = { uri, bitmap ->
                                capturedImageUri = uri
                                capturedBitmap = bitmap
                                showCamera = false
                            }
                        )

                        // 添加人脸检测引导框
                        FaceDetectionGuide()
                    } else {
                        // 显示拍摄的照片
                        capturedBitmap?.let { bitmap ->
                            Image(
                                bitmap = bitmap.asImageBitmap(),
                                contentDescription = "Captured Photo",
                                modifier = Modifier.fillMaxSize(),
                                contentScale = ContentScale.Fit
                            )
                        }

                        // 控制按钮
                        Row(
                            modifier = Modifier
                                .align(androidx.compose.ui.Alignment.TopEnd)
                                .padding(16.dp),
                            horizontalArrangement = Arrangement.spacedBy(8.dp)
                        ) {
                            Button(
                                onClick = {
                                    showCamera = true
                                    faces = emptyList()
                                    capturedImageUri = null
                                    capturedBitmap = null
                                }
                            ) {
                                Text("返回摄像头")
                            }

                            Button(
                                onClick = {
                                    // 使用测试图片进行人脸检测
                                    testFaceDetection(this@MainActivity) { testFaces, testWidth, testHeight, testBitmap ->
                                        faces = testFaces
                                        imageWidth = testWidth
                                        imageHeight = testHeight
                                        capturedBitmap = testBitmap
                                    }
                                }
                            ) {
                                Text("测试检测")
                            }
                        }
                    }

                    FaceOverlay(faces = faces, imageWidth = imageWidth, imageHeight = imageHeight)
                }
            }
        }
    }
}

@Composable
fun CameraView(
    isBackCamera: Boolean,
    onFacesDetected: (List<Face>, Int, Int) -> Unit,
    onCameraSwitch: () -> Unit,
    onImageCaptured: (Uri?, Bitmap?) -> Unit
) {
    val context = LocalContext.current
    val lifecycleOwner = LocalLifecycleOwner.current
    val cameraProviderFuture = remember { ProcessCameraProvider.getInstance(context) }
    val cameraExecutor = remember { Executors.newSingleThreadExecutor() }
    var imageCapture by remember { mutableStateOf<ImageCapture?>(null) }
    var cameraProvider by remember { mutableStateOf<ProcessCameraProvider?>(null) }
    var previewView by remember { mutableStateOf<PreviewView?>(null) }

    val faceDetectorOptions = FaceDetectorOptions.Builder()
        .setPerformanceMode(FaceDetectorOptions.PERFORMANCE_MODE_ACCURATE)
        .setLandmarkMode(FaceDetectorOptions.LANDMARK_MODE_ALL)
        .setClassificationMode(FaceDetectorOptions.CLASSIFICATION_MODE_ALL)
        .setContourMode(FaceDetectorOptions.CONTOUR_MODE_ALL)
        .setMinFaceSize(0.05f)  // 降低最小人脸大小阈值
        .enableTracking()
        .build()
    val faceDetector = FaceDetection.getClient(faceDetectorOptions)

    // 绑定摄像头的函数
    fun bindCamera(preview: PreviewView) {
        Log.d("CameraView", "bindCamera called, isBackCamera: $isBackCamera")
        cameraProviderFuture.addListener({
            try {
                cameraProvider = cameraProviderFuture.get()
                Log.d("CameraView", "CameraProvider obtained")

                val previewUseCase = Preview.Builder().build().also {
                    it.setSurfaceProvider(preview.surfaceProvider)
                }

                imageCapture = ImageCapture.Builder()
                    .setCaptureMode(ImageCapture.CAPTURE_MODE_MAXIMIZE_QUALITY)
                    .build()

                val cameraSelector = if (isBackCamera) {
                    CameraSelector.DEFAULT_BACK_CAMERA
                } else {
                    CameraSelector.DEFAULT_FRONT_CAMERA
                }

                cameraProvider?.unbindAll()
                cameraProvider?.bindToLifecycle(
                    lifecycleOwner,
                    cameraSelector,
                    previewUseCase,
                    imageCapture
                )
                Log.d("CameraView", "Camera bound successfully")
            } catch (exc: Exception) {
                Log.e("CameraView", "Use case binding failed", exc)
            }
        }, androidx.core.content.ContextCompat.getMainExecutor(context))
    }

    Box(modifier = Modifier.fillMaxSize()) {
        AndroidView(
            modifier = Modifier.fillMaxSize(),
            factory = { ctx ->
                val preview = PreviewView(ctx)
                previewView = preview
                Log.d("CameraView", "PreviewView created")
                bindCamera(preview)
                preview
            },
            update = { preview ->
                Log.d("CameraView", "AndroidView update called")
                bindCamera(preview)
            }
        )

        // 新氧医美风格控制面板
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .align(androidx.compose.ui.Alignment.BottomCenter)
                .padding(16.dp)
                .shadow(12.dp, RoundedCornerShape(topStart = 24.dp, topEnd = 24.dp)),
            shape = RoundedCornerShape(topStart = 24.dp, topEnd = 24.dp),
            colors = CardDefaults.cardColors(
                containerColor = Color.Black.copy(alpha = 0.8f)
            )
        ) {
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(24.dp),
                horizontalArrangement = Arrangement.SpaceEvenly,
                verticalAlignment = androidx.compose.ui.Alignment.CenterVertically
            ) {
                // Camera switch button - 小圆按钮
                FloatingActionButton(
                    onClick = {
                        Log.d("CameraView", "Camera switch button clicked")
                        onCameraSwitch()
                    },
                    modifier = Modifier.size(56.dp),
                    containerColor = Color.White.copy(alpha = 0.1f),
                    contentColor = Color.White
                ) {
                    Text(
                        text = "🔄",
                        style = MaterialTheme.typography.headlineSmall
                    )
                }

                // Capture button - 大圆按钮（新氧风格）
                FloatingActionButton(
                    onClick = {
                        Log.d("CameraView", "Capture button clicked, imageCapture: $imageCapture")
                        imageCapture?.let { capture ->
                            takePicture(capture, context, faceDetector, onFacesDetected, onImageCaptured)
                        } ?: Log.w("CameraView", "ImageCapture is null")
                    },
                    modifier = Modifier
                        .size(80.dp)
                        .shadow(8.dp, CircleShape),
                    containerColor = Color.Transparent
                ) {
                    Box(
                        modifier = Modifier
                            .fillMaxSize()
                            .background(
                                brush = Brush.radialGradient(
                                    colors = listOf(
                                        Color.Cyan,
                                        Color.Blue,
                                        Color.Magenta
                                    )
                                ),
                                shape = CircleShape
                            ),
                        contentAlignment = androidx.compose.ui.Alignment.Center
                    ) {
                        Text(
                            text = "📸",
                            style = MaterialTheme.typography.headlineLarge.copy(
                                color = Color.White
                            )
                        )
                    }
                }

                // Test button - 小圆按钮
                FloatingActionButton(
                    onClick = {
                        testFaceDetection(context) { testFaces, testWidth, testHeight, testBitmap ->
                            onFacesDetected(testFaces, testWidth, testHeight)
                            onImageCaptured(null, testBitmap)
                        }
                    },
                    modifier = Modifier.size(56.dp),
                    containerColor = Color.White.copy(alpha = 0.1f),
                    contentColor = Color.White
                ) {
                    Text(
                        text = "🧪",
                        style = MaterialTheme.typography.headlineSmall
                    )
                }
            }
        }
    }
}

private fun takePicture(
    imageCapture: ImageCapture,
    context: android.content.Context,
    faceDetector: com.google.mlkit.vision.face.FaceDetector,
    onFacesDetected: (List<Face>, Int, Int) -> Unit,
    onImageCaptured: (Uri?, Bitmap?) -> Unit
) {
    val name = SimpleDateFormat("yyyy-MM-dd-HH-mm-ss-SSS", Locale.US)
        .format(System.currentTimeMillis())
    val contentValues = android.content.ContentValues().apply {
        put(android.provider.MediaStore.MediaColumns.DISPLAY_NAME, name)
        put(android.provider.MediaStore.MediaColumns.MIME_TYPE, "image/jpeg")
        if (android.os.Build.VERSION.SDK_INT > android.os.Build.VERSION_CODES.P) {
            put(android.provider.MediaStore.Images.Media.RELATIVE_PATH, "Pictures/FaceDetection")
        }
    }

    val outputOptions = ImageCapture.OutputFileOptions.Builder(
        context.contentResolver,
        android.provider.MediaStore.Images.Media.EXTERNAL_CONTENT_URI,
        contentValues
    ).build()

    imageCapture.takePicture(
        outputOptions,
        androidx.core.content.ContextCompat.getMainExecutor(context),
        object : ImageCapture.OnImageSavedCallback {
            override fun onError(exception: ImageCaptureException) {
                Log.e("CameraView", "Photo capture failed: ${exception.message}", exception)
            }

            override fun onImageSaved(output: ImageCapture.OutputFileResults) {
                Log.d("CameraView", "Photo capture succeeded: ${output.savedUri}")

                // Load the captured image as bitmap and process for face detection
                output.savedUri?.let { uri ->
                    try {
                        // Load bitmap from URI and handle rotation
                        val bitmap = loadAndRotateBitmap(context, uri)

                        if (bitmap != null) {
                            // Create InputImage from bitmap for face detection
                            val inputImage = InputImage.fromBitmap(bitmap, 0)

                            // Perform face detection
                            faceDetector.process(inputImage)
                                .addOnSuccessListener { detectedFaces ->
                                    Log.d("CameraView", "Face detection completed, faces found: ${detectedFaces.size}")
                                    Log.d("CameraView", "Image dimensions: ${bitmap.width} x ${bitmap.height}")
                                    detectedFaces.forEachIndexed { index, face ->
                                        Log.d("CameraView", "Face $index: boundingBox=${face.boundingBox}, landmarks=${face.allLandmarks.size}")
                                    }
                                    onFacesDetected(detectedFaces, bitmap.width, bitmap.height)
                                }
                                .addOnFailureListener { e ->
                                    Log.e("CameraView", "Face detection on captured image failed", e)
                                }

                            // Call the callback with the captured image
                            onImageCaptured(uri, bitmap)
                        } else {
                            Log.e("CameraView", "Failed to decode bitmap from URI")
                        }
                    } catch (e: Exception) {
                        Log.e("CameraView", "Error processing captured image", e)
                    }
                }
            }
        }
    )
}

private fun loadAndRotateBitmap(context: android.content.Context, uri: Uri): Bitmap? {
    return try {
        // 首先加载图片
        val inputStream = context.contentResolver.openInputStream(uri)
        val bitmap = BitmapFactory.decodeStream(inputStream)
        inputStream?.close()

        if (bitmap == null) return null

        // 获取图片的EXIF信息来确定旋转角度
        val exifInputStream = context.contentResolver.openInputStream(uri)
        val exif = ExifInterface(exifInputStream!!)
        exifInputStream.close()

        val orientation = exif.getAttributeInt(
            ExifInterface.TAG_ORIENTATION,
            ExifInterface.ORIENTATION_NORMAL
        )

        val rotationAngle = when (orientation) {
            ExifInterface.ORIENTATION_ROTATE_90 -> 90f
            ExifInterface.ORIENTATION_ROTATE_180 -> 180f
            ExifInterface.ORIENTATION_ROTATE_270 -> 270f
            else -> 0f
        }

        Log.d("ImageRotation", "Original orientation: $orientation, rotation needed: $rotationAngle")

        if (rotationAngle != 0f) {
            // 创建旋转矩阵
            val matrix = Matrix()
            matrix.postRotate(rotationAngle)

            // 应用旋转
            val rotatedBitmap = Bitmap.createBitmap(
                bitmap, 0, 0, bitmap.width, bitmap.height, matrix, true
            )

            // 回收原始bitmap
            if (rotatedBitmap != bitmap) {
                bitmap.recycle()
            }

            Log.d("ImageRotation", "Rotated bitmap: ${rotatedBitmap.width}x${rotatedBitmap.height}")
            rotatedBitmap
        } else {
            bitmap
        }
    } catch (e: Exception) {
        Log.e("ImageRotation", "Error loading and rotating bitmap", e)
        null
    }
}

private fun testFaceDetection(
    context: android.content.Context,
    onFacesDetected: (List<Face>, Int, Int, Bitmap) -> Unit
) {
    // 创建一个简单的测试图片（纯色图片用于测试坐标系统）
    val testBitmap = Bitmap.createBitmap(400, 300, Bitmap.Config.ARGB_8888)
    testBitmap.eraseColor(android.graphics.Color.BLUE)

    // 在图片上绘制一些测试图形
    val canvas = android.graphics.Canvas(testBitmap)
    val paint = android.graphics.Paint().apply {
        color = android.graphics.Color.RED
        strokeWidth = 5f
        style = android.graphics.Paint.Style.STROKE
    }

    // 绘制一个矩形作为"假人脸"用于测试坐标
    canvas.drawRect(100f, 75f, 300f, 225f, paint)

    // 绘制一些圆点作为"假关键点"
    val pointPaint = android.graphics.Paint().apply {
        color = android.graphics.Color.YELLOW
        style = android.graphics.Paint.Style.FILL
    }
    canvas.drawCircle(150f, 125f, 8f, pointPaint) // 左眼
    canvas.drawCircle(250f, 125f, 8f, pointPaint) // 右眼
    canvas.drawCircle(200f, 150f, 8f, pointPaint) // 鼻子
    canvas.drawCircle(200f, 175f, 8f, pointPaint) // 嘴巴

    Log.d("TestFaceDetection", "Created test bitmap: ${testBitmap.width} x ${testBitmap.height}")

    // 创建一个假的人脸检测结果用于测试坐标转换
    val testFaces = emptyList<Face>() // 暂时使用空列表，主要测试图片显示和坐标系统
    onFacesDetected(testFaces, testBitmap.width, testBitmap.height, testBitmap)
}

@Composable
fun FaceDetectionGuide() {
    Box(modifier = Modifier.fillMaxSize()) {
        // 渐变背景遮罩 - 新氧风格
        Canvas(modifier = Modifier.fillMaxSize()) {
            val viewWidth = size.width
            val viewHeight = size.height

            // 计算检测框的位置和大小 - 增大尺寸
            val frameWidth = viewWidth * 0.85f  // 从0.7f增加到0.85f
            val frameHeight = frameWidth * 1.3f // 椭圆形，稍微高一些，从1.2f增加到1.3f
            val centerX = viewWidth / 2f
            val centerY = viewHeight / 2f - 30.dp.toPx() // 稍微向上偏移，减少偏移量

            val left = centerX - frameWidth / 2f
            val top = centerY - frameHeight / 2f
            val right = centerX + frameWidth / 2f
            val bottom = centerY + frameHeight / 2f

            // 绘制渐变遮罩背景（除了检测框区域）- 新氧风格
            val maskBrush = Brush.radialGradient(
                colors = listOf(
                    Color.Black.copy(alpha = 0.3f),
                    Color.Black.copy(alpha = 0.7f)
                ),
                center = Offset(centerX, centerY),
                radius = frameWidth * 0.8f
            )

            // 上方区域
            drawRect(
                brush = Brush.verticalGradient(
                    colors = listOf(
                        Color.Black.copy(alpha = 0.8f),
                        Color.Black.copy(alpha = 0.4f)
                    )
                ),
                topLeft = Offset(0f, 0f),
                size = androidx.compose.ui.geometry.Size(viewWidth, top)
            )
            // 下方区域
            drawRect(
                brush = Brush.verticalGradient(
                    colors = listOf(
                        Color.Black.copy(alpha = 0.4f),
                        Color.Black.copy(alpha = 0.8f)
                    )
                ),
                topLeft = Offset(0f, bottom),
                size = androidx.compose.ui.geometry.Size(viewWidth, viewHeight - bottom)
            )
            // 左侧区域
            drawRect(
                color = Color.Black.copy(alpha = 0.6f),
                topLeft = Offset(0f, top),
                size = androidx.compose.ui.geometry.Size(left, frameHeight)
            )
            // 右侧区域
            drawRect(
                color = Color.Black.copy(alpha = 0.6f),
                topLeft = Offset(right, top),
                size = androidx.compose.ui.geometry.Size(viewWidth - right, frameHeight)
            )

            // 绘制主检测框边框（椭圆形）- 新氧风格双层设计
            // 外层发光效果
            drawOval(
                color = Color.Cyan.copy(alpha = 0.3f),
                topLeft = Offset(left - 4.dp.toPx(), top - 4.dp.toPx()),
                size = androidx.compose.ui.geometry.Size(frameWidth + 8.dp.toPx(), frameHeight + 8.dp.toPx()),
                style = Stroke(width = 8.dp.toPx())
            )

            // 主检测框
            drawOval(
                brush = Brush.sweepGradient(
                    colors = listOf(
                        Color.Cyan,
                        Color.Blue,
                        Color.Magenta,
                        Color.Cyan
                    ),
                    center = Offset(centerX, centerY)
                ),
                topLeft = Offset(left, top),
                size = androidx.compose.ui.geometry.Size(frameWidth, frameHeight),
                style = Stroke(
                    width = 4.dp.toPx(),
                    pathEffect = PathEffect.dashPathEffect(floatArrayOf(25f, 15f), 0f)
                )
            )

            // 内层精细线条
            drawOval(
                color = Color.White.copy(alpha = 0.8f),
                topLeft = Offset(left + 2.dp.toPx(), top + 2.dp.toPx()),
                size = androidx.compose.ui.geometry.Size(frameWidth - 4.dp.toPx(), frameHeight - 4.dp.toPx()),
                style = Stroke(width = 1.dp.toPx())
            )

            // 绘制四个角的装饰线 - 新氧风格科技感设计
            val cornerLength = 40.dp.toPx()
            val cornerOffset = 20.dp.toPx()
            val cornerStroke = Stroke(
                width = 5.dp.toPx(),
                cap = StrokeCap.Round
            )

            // 左上角 - 渐变色装饰
            drawLine(
                brush = Brush.linearGradient(
                    colors = listOf(Color.Cyan, Color.Blue),
                    start = Offset(left - cornerOffset, top),
                    end = Offset(left - cornerOffset + cornerLength, top)
                ),
                start = Offset(left - cornerOffset, top),
                end = Offset(left - cornerOffset + cornerLength, top),
                strokeWidth = cornerStroke.width,
                cap = cornerStroke.cap
            )
            drawLine(
                brush = Brush.linearGradient(
                    colors = listOf(Color.Cyan, Color.Blue),
                    start = Offset(left, top - cornerOffset),
                    end = Offset(left, top - cornerOffset + cornerLength)
                ),
                start = Offset(left, top - cornerOffset),
                end = Offset(left, top - cornerOffset + cornerLength),
                strokeWidth = cornerStroke.width,
                cap = cornerStroke.cap
            )

            // 右上角
            drawLine(
                brush = Brush.linearGradient(
                    colors = listOf(Color.Blue, Color.Magenta),
                    start = Offset(right + cornerOffset, top),
                    end = Offset(right + cornerOffset - cornerLength, top)
                ),
                start = Offset(right + cornerOffset, top),
                end = Offset(right + cornerOffset - cornerLength, top),
                strokeWidth = cornerStroke.width,
                cap = cornerStroke.cap
            )
            drawLine(
                brush = Brush.linearGradient(
                    colors = listOf(Color.Blue, Color.Magenta),
                    start = Offset(right, top - cornerOffset),
                    end = Offset(right, top - cornerOffset + cornerLength)
                ),
                start = Offset(right, top - cornerOffset),
                end = Offset(right, top - cornerOffset + cornerLength),
                strokeWidth = cornerStroke.width,
                cap = cornerStroke.cap
            )

            // 左下角
            drawLine(
                brush = Brush.linearGradient(
                    colors = listOf(Color.Magenta, Color.Red),
                    start = Offset(left - cornerOffset, bottom),
                    end = Offset(left - cornerOffset + cornerLength, bottom)
                ),
                start = Offset(left - cornerOffset, bottom),
                end = Offset(left - cornerOffset + cornerLength, bottom),
                strokeWidth = cornerStroke.width,
                cap = cornerStroke.cap
            )
            drawLine(
                brush = Brush.linearGradient(
                    colors = listOf(Color.Magenta, Color.Red),
                    start = Offset(left, bottom + cornerOffset),
                    end = Offset(left, bottom + cornerOffset - cornerLength)
                ),
                start = Offset(left, bottom + cornerOffset),
                end = Offset(left, bottom + cornerOffset - cornerLength),
                strokeWidth = cornerStroke.width,
                cap = cornerStroke.cap
            )

            // 右下角
            drawLine(
                brush = Brush.linearGradient(
                    colors = listOf(Color.Red, Color.Cyan),
                    start = Offset(right + cornerOffset, bottom),
                    end = Offset(right + cornerOffset - cornerLength, bottom)
                ),
                start = Offset(right + cornerOffset, bottom),
                end = Offset(right + cornerOffset - cornerLength, bottom),
                strokeWidth = cornerStroke.width,
                cap = cornerStroke.cap
            )
            drawLine(
                brush = Brush.linearGradient(
                    colors = listOf(Color.Red, Color.Cyan),
                    start = Offset(right, bottom + cornerOffset),
                    end = Offset(right, bottom + cornerOffset - cornerLength)
                ),
                start = Offset(right, bottom + cornerOffset),
                end = Offset(right, bottom + cornerOffset - cornerLength),
                strokeWidth = cornerStroke.width,
                cap = cornerStroke.cap
            )
        }

        // 顶部提示卡片 - 新氧医美风格
        Card(
            modifier = Modifier
                .align(androidx.compose.ui.Alignment.TopCenter)
                .padding(top = 60.dp, start = 24.dp, end = 24.dp)
                .shadow(8.dp, RoundedCornerShape(20.dp)),
            shape = RoundedCornerShape(20.dp),
            colors = CardDefaults.cardColors(
                containerColor = Color.Black.copy(alpha = 0.7f)
            )
        ) {
            Column(
                modifier = Modifier.padding(20.dp),
                horizontalAlignment = androidx.compose.ui.Alignment.CenterHorizontally
            ) {
                Text(
                    text = "✨ AI 人脸检测",
                    style = MaterialTheme.typography.headlineSmall.copy(
                        color = Color.White,
                        fontWeight = FontWeight.Bold
                    ),
                    textAlign = TextAlign.Center
                )
                Spacer(modifier = Modifier.height(8.dp))
                Text(
                    text = "请将面部对准检测框",
                    style = MaterialTheme.typography.bodyMedium.copy(
                        color = Color.White.copy(alpha = 0.9f)
                    ),
                    textAlign = TextAlign.Center
                )
                Spacer(modifier = Modifier.height(4.dp))
                Text(
                    text = "保持光线充足，面部清晰可见",
                    style = MaterialTheme.typography.bodySmall.copy(
                        color = Color.Cyan.copy(alpha = 0.8f)
                    ),
                    textAlign = TextAlign.Center
                )
            }
        }

        // 底部状态指示
        Card(
            modifier = Modifier
                .align(androidx.compose.ui.Alignment.BottomCenter)
                .padding(bottom = 120.dp, start = 24.dp, end = 24.dp)
                .shadow(6.dp, RoundedCornerShape(16.dp)),
            shape = RoundedCornerShape(16.dp),
            colors = CardDefaults.cardColors(
                containerColor = Color.Black.copy(alpha = 0.6f)
            )
        ) {
            Row(
                modifier = Modifier.padding(16.dp),
                verticalAlignment = androidx.compose.ui.Alignment.CenterVertically
            ) {
                // 状态指示灯
                Box(
                    modifier = Modifier
                        .size(12.dp)
                        .background(
                            brush = Brush.radialGradient(
                                colors = listOf(Color.Green, Color.Green.copy(alpha = 0.3f))
                            ),
                            shape = CircleShape
                        )
                )
                Spacer(modifier = Modifier.padding(8.dp))
                Text(
                    text = "准备就绪 · 点击拍照按钮开始检测",
                    style = MaterialTheme.typography.bodySmall.copy(
                        color = Color.White.copy(alpha = 0.9f)
                    )
                )
            }
        }
    }
}

@Composable
fun FaceOverlay(faces: List<Face>, imageWidth: Int, imageHeight: Int) {
    Canvas(modifier = Modifier.fillMaxSize()) {
        if (imageWidth == 0 || imageHeight == 0 || faces.isEmpty()) return@Canvas

        val viewWidth = size.width
        val viewHeight = size.height

        // 计算图片在视图中的实际显示区域（考虑ContentScale.Fit）
        val imageAspectRatio = imageWidth.toFloat() / imageHeight.toFloat()
        val viewAspectRatio = viewWidth / viewHeight

        val (displayWidth, displayHeight, offsetX, offsetY) = if (imageAspectRatio > viewAspectRatio) {
            // 图片更宽，以宽度为准
            val displayWidth = viewWidth
            val displayHeight = viewWidth / imageAspectRatio
            val offsetY = (viewHeight - displayHeight) / 2f
            arrayOf(displayWidth, displayHeight, 0f, offsetY)
        } else {
            // 图片更高，以高度为准
            val displayHeight = viewHeight
            val displayWidth = viewHeight * imageAspectRatio
            val offsetX = (viewWidth - displayWidth) / 2f
            arrayOf(displayWidth, displayHeight, offsetX, 0f)
        }

        val scaleX = displayWidth / imageWidth.toFloat()
        val scaleY = displayHeight / imageHeight.toFloat()

        Log.d("FaceOverlay", "View: ${viewWidth}x${viewHeight}, Image: ${imageWidth}x${imageHeight}")
        Log.d("FaceOverlay", "Display: ${displayWidth}x${displayHeight}, Offset: (${offsetX}, ${offsetY})")
        Log.d("FaceOverlay", "Scale: (${scaleX}, ${scaleY})")

        for (face in faces) {
            // Draw bounding box
            val boundingBox = face.boundingBox
            val left = offsetX + boundingBox.left * scaleX
            val top = offsetY + boundingBox.top * scaleY
            val right = offsetX + boundingBox.right * scaleX
            val bottom = offsetY + boundingBox.bottom * scaleY

            drawRect(
                color = Color.Red,
                topLeft = Offset(left, top),
                size = androidx.compose.ui.geometry.Size(right - left, bottom - top),
                style = Stroke(width = 3f)
            )

            // Draw face contours
            drawFaceContours(face, displayWidth, displayHeight, offsetX, offsetY, scaleX, scaleY)

            // Draw landmarks with different colors for different types
            face.allLandmarks.forEach { landmark ->
                val landmarkPosition = landmark.position
                val x = offsetX + landmarkPosition.x * scaleX
                val y = offsetY + landmarkPosition.y * scaleY

                val color = when (landmark.landmarkType) {
                    com.google.mlkit.vision.face.FaceLandmark.MOUTH_BOTTOM,
                    com.google.mlkit.vision.face.FaceLandmark.MOUTH_LEFT,
                    com.google.mlkit.vision.face.FaceLandmark.MOUTH_RIGHT -> Color.Magenta
                    com.google.mlkit.vision.face.FaceLandmark.LEFT_EYE,
                    com.google.mlkit.vision.face.FaceLandmark.RIGHT_EYE -> Color.Cyan
                    com.google.mlkit.vision.face.FaceLandmark.NOSE_BASE -> Color.Green
                    com.google.mlkit.vision.face.FaceLandmark.LEFT_EAR,
                    com.google.mlkit.vision.face.FaceLandmark.RIGHT_EAR -> Color.Blue
                    else -> Color.Yellow
                }

                drawCircle(
                    color = color,
                    center = Offset(x, y),
                    radius = 5f
                )
            }
        }
    }
}

fun DrawScope.drawFaceContours(face: Face, displayWidth: Float, displayHeight: Float, offsetX: Float, offsetY: Float, scaleX: Float, scaleY: Float) {
    val contourTypes = listOf(
        FaceContour.FACE,
        FaceContour.LEFT_EYEBROW_TOP,
        FaceContour.LEFT_EYEBROW_BOTTOM,
        FaceContour.RIGHT_EYEBROW_TOP,
        FaceContour.RIGHT_EYEBROW_BOTTOM,
        FaceContour.LEFT_EYE,
        FaceContour.RIGHT_EYE,
        FaceContour.UPPER_LIP_TOP,
        FaceContour.UPPER_LIP_BOTTOM,
        FaceContour.LOWER_LIP_TOP,
        FaceContour.LOWER_LIP_BOTTOM,
        FaceContour.NOSE_BRIDGE,
        FaceContour.NOSE_BOTTOM
    )

    contourTypes.forEach { contourType ->
        val contour = face.getContour(contourType)
        contour?.let { faceContour ->
            val points = faceContour.points
            if (points.size > 1) {
                val path = Path()
                val firstPoint = points[0]
                val startX = offsetX + firstPoint.x * scaleX
                val startY = offsetY + firstPoint.y * scaleY
                path.moveTo(startX, startY)

                for (i in 1 until points.size) {
                    val point = points[i]
                    val x = offsetX + point.x * scaleX
                    val y = offsetY + point.y * scaleY
                    path.lineTo(x, y)
                }

                // Close path for certain contours
                if (contourType == FaceContour.FACE ||
                    contourType == FaceContour.LEFT_EYE ||
                    contourType == FaceContour.RIGHT_EYE) {
                    path.close()
                }

                val color = when (contourType) {
                    FaceContour.FACE -> Color.Red
                    FaceContour.LEFT_EYE, FaceContour.RIGHT_EYE -> Color.Cyan
                    FaceContour.LEFT_EYEBROW_TOP, FaceContour.LEFT_EYEBROW_BOTTOM,
                    FaceContour.RIGHT_EYEBROW_TOP, FaceContour.RIGHT_EYEBROW_BOTTOM -> Color.Blue
                    FaceContour.UPPER_LIP_TOP, FaceContour.UPPER_LIP_BOTTOM,
                    FaceContour.LOWER_LIP_TOP, FaceContour.LOWER_LIP_BOTTOM -> Color.Magenta
                    FaceContour.NOSE_BRIDGE, FaceContour.NOSE_BOTTOM -> Color.Green
                    else -> Color.White
                }

                drawPath(
                    path = path,
                    color = color,
                    style = Stroke(width = 2f)
                )
            }
        }
    }
}